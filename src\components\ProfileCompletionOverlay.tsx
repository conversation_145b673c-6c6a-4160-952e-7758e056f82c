// Profile Completion Overlay Component
// Mandatory overlay that appears on first login to force profile completion

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  LinearProgress,
  Alert,
  Paper,
  IconButton,
  Chip
} from '@mui/material';
import {
  Person as PersonIcon,
  LocalHospital as MedicalIcon,
  ContactPhone as ContactIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { checkProfileCompletion, getCompletionSummary, type ProfileCompletionResult } from '../services/profileCompletionService';
import StudentProfileForm from './StudentProfileForm';

interface ProfileCompletionOverlayProps {
  open: boolean;
  onComplete: () => void;
}

const steps = [
  {
    label: 'Personal Details',
    icon: <PersonIcon />,
    description: 'Basic information and contact details'
  },
  {
    label: 'Medical Information',
    icon: <MedicalIcon />,
    description: 'Health information and medical history'
  },
  {
    label: 'Emergency Contact',
    icon: <ContactIcon />,
    description: 'Emergency contact person details'
  }
];

const ProfileCompletionOverlay: React.FC<ProfileCompletionOverlayProps> = ({
  open,
  onComplete
}) => {
  const { userProfile, currentUser } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [completionStatus, setCompletionStatus] = useState<ProfileCompletionResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);

  // Check completion status on mount
  useEffect(() => {
    const checkCompletion = async () => {
      if (!userProfile || !currentUser) return;
      
      try {
        setLoading(true);
        const status = checkProfileCompletion(userProfile);
        setCompletionStatus(status);
        
        // Set active step to first incomplete section
        if (!status.personalDetailsComplete) {
          setActiveStep(0);
        } else if (!status.medicalInfoComplete) {
          setActiveStep(1);
        } else if (!status.emergencyContactComplete) {
          setActiveStep(2);
        }
        
      } catch (error) {
        console.error('Error checking profile completion:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      checkCompletion();
    }
  }, [open, userProfile, currentUser]);

  const handleStartCompletion = () => {
    setShowForm(true);
  };

  const handleFormComplete = () => {
    setShowForm(false);
    onComplete();
  };

  const getStepStatus = (stepIndex: number) => {
    if (!completionStatus) return 'pending';
    
    switch (stepIndex) {
      case 0:
        return completionStatus.personalDetailsComplete ? 'completed' : 'pending';
      case 1:
        return completionStatus.medicalInfoComplete ? 'completed' : 'pending';
      case 2:
        return completionStatus.emergencyContactComplete ? 'completed' : 'pending';
      default:
        return 'pending';
    }
  };

  if (showForm) {
    return (
      <StudentProfileForm
        open={true}
        onClose={handleFormComplete}
        initialStep={activeStep}
        isOverlay={true}
      />
    );
  }

  return (
    <Dialog
      open={open}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '500px'
          
        }
      }}
    >
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
          <WarningIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
          <Typography variant="h4" fontWeight="bold">
            Complete Your Profile
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Please complete all sections to access the platform
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ px: 4, pb: 4 }}>
        {loading ? (
          <Box sx={{ py: 4 }}>
            <LinearProgress />
            <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
              Checking profile status...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Progress Overview */}
            {completionStatus && (
              <Paper sx={{ p: 3, mb: 4, bgcolor: 'grey.50' ,boxShadow: '0 4px 20px rgba(0,0,0,0.08)'}}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Profile Completion Progress
                  </Typography>
                  <Chip
                    label={`${completionStatus.completionPercentage}% Complete`}
                    color={completionStatus.isComplete ? 'success' : 'warning'}
                    variant="filled"
                  />
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={completionStatus.completionPercentage}
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {getCompletionSummary(completionStatus)}
                </Typography>
              </Paper>
            )}

            {/* Steps Overview */}
            <Stepper activeStep={activeStep} orientation="vertical" sx={{ mb: 4 }}>
              {steps.map((step, index) => {
                const stepStatus = getStepStatus(index);
                
                return (
                  <Step key={step.label}>
                    <StepLabel
                      StepIconComponent={() => (
                        <Box
                          sx={{
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            bgcolor: stepStatus === 'completed' ? 'success.main' : 'grey.300',
                            color: stepStatus === 'completed' ? 'white' : 'grey.600'
                            
                          }}
                        >
                          {stepStatus === 'completed' ? <CheckIcon /> : step.icon}
                        </Box>
                      )}
                    >
                      <Box sx={{ ml: 2 }}>
                        <Typography variant="h6" fontWeight="medium">
                          {step.label}
                          {stepStatus === 'completed' && (
                            <CheckIcon color="success" sx={{ ml: 1, fontSize: 20 }} />
                          )}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {step.description}
                        </Typography>
                      </Box>
                    </StepLabel>
                  </Step>
                );
              })}
            </Stepper>

            {/* Warning Message */}
            <Alert severity="warning" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Important:</strong> You must complete all sections before you can access the platform. 
                This information is required for your safety and to provide you with the best healthcare services.
              </Typography>
            </Alert>

            {/* Action Button */}
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="contained"
                size="large"
                onClick={handleStartCompletion}
                sx={{
                  px: 4,
                  py: 1.5,
                  borderRadius: 2,
                  fontSize: '1.1rem'
                }}
              >
                {completionStatus?.completionPercentage === 0 
                  ? 'Start Profile Setup' 
                  : 'Continue Profile Setup'
                }
              </Button>
            </Box>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ProfileCompletionOverlay;
