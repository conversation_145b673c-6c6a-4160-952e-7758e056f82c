import React, { useState, useEffect } from 'react';
import {
  Container, Typography, Box, Grid, Paper, Button,
  List, ListItem, ListItemIcon, ListItemText, Avatar,
  Card, CardContent, Chip, Badge, IconButton, Divider,
  LinearProgress, Alert
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArticleIcon from '@mui/icons-material/Article';
import AddIcon from '@mui/icons-material/Add';
import PeopleIcon from '@mui/icons-material/People';
import EventIcon from '@mui/icons-material/Event';
import ChatIcon from '@mui/icons-material/Chat';
import NotificationsIcon from '@mui/icons-material/Notifications';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PersonIcon from '@mui/icons-material/Person';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { useFirebase } from '../../contexts/FirebaseContext';

const DoctorDashboard = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const [loading, setLoading] = useState(true);

  // Mock doctor data
  const [doctorStats, setDoctorStats] = useState({
    totalPatients: 145,
    todayAppointments: 8,
    pendingChats: 12,
    completedToday: 5
  });

  // Mock data for today's appointments
  const [todayAppointments, setTodayAppointments] = useState([
    { id: 1, patient: 'John Smith', time: '09:00 AM', type: 'Consultation', status: 'upcoming' },
    { id: 2, patient: 'Emma Davis', time: '10:30 AM', type: 'Follow-up', status: 'upcoming' },
    { id: 3, patient: 'Alex Johnson', time: '02:00 PM', type: 'Check-up', status: 'completed' },
    { id: 4, patient: 'Sarah Wilson', time: '03:30 PM', type: 'Consultation', status: 'upcoming' }
  ]);

  // Mock data for pending chats
  const [pendingChats, setPendingChats] = useState([
    { id: 1, patient: 'Michael Brown', lastMessage: 'I have been experiencing headaches...', time: '5 min ago', unread: 3 },
    { id: 2, patient: 'Lisa Garcia', lastMessage: 'Thank you for the prescription', time: '15 min ago', unread: 1 },
    { id: 3, patient: 'David Lee', lastMessage: 'When should I schedule my next appointment?', time: '1 hour ago', unread: 2 }
  ]);

  // Mock data for health resources
  const healthResources = [
    { id: 1, title: 'Managing Exam Stress', category: 'Mental Health', status: 'published', date: 'May 5, 2023' },
    { id: 2, title: 'Nutrition Tips for Students', category: 'Nutrition', status: 'published', date: 'May 3, 2023' },
    { id: 3, title: 'Sleep Hygiene Guide', category: 'Wellness', status: 'draft', date: 'April 28, 2023' }
  ];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Dashboard Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Doctor Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Welcome back, Dr. {currentUser?.displayName || 'User'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <IconButton
              color="primary"
              onClick={() => navigate('/doctor/chat')}
              sx={{ position: 'relative' }}
            >
              <Badge badgeContent={doctorStats.pendingChats} color="error">
                <ChatIcon />
              </Badge>
            </IconButton>
            <IconButton color="primary">
              <Badge badgeContent={5} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="primary.main">
                      {doctorStats.totalPatients}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Patients
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.light', color: 'primary.main' }}>
                    <PeopleIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="success.main">
                      {doctorStats.todayAppointments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Today's Appointments
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.light', color: 'success.main' }}>
                    <EventIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="warning.main">
                      {doctorStats.pendingChats}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Chats
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.light', color: 'warning.main' }}>
                    <ChatIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color="info.main">
                      {doctorStats.completedToday}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completed Today
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.light', color: 'info.main' }}>
                    <TrendingUpIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Quick Actions */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PeopleIcon />}
                    onClick={() => navigate('/doctor/patients')}
                    sx={{ borderRadius: 2, py: 1.5 }}
                  >
                    View Patients
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<EventIcon />}
                    onClick={() => navigate('/doctor/appointments')}
                    sx={{ borderRadius: 2, py: 1.5 }}
                  >
                    Appointments
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ChatIcon />}
                    onClick={() => navigate('/doctor/chat')}
                    sx={{ borderRadius: 2, py: 1.5 }}
                  >
                    Chat Center
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PersonIcon />}
                    onClick={() => navigate('/doctor/profile')}
                    sx={{ borderRadius: 2, py: 1.5 }}
                  >
                    My Profile
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Today's Schedule
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/doctor/appointments')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {todayAppointments.slice(0, 4).map((appointment) => (
                  <ListItem key={appointment.id} sx={{ px: 0, py: 1 }}>
                    <ListItemIcon>
                      <FiberManualRecordIcon
                        sx={{
                          color: appointment.status === 'completed' ? 'success.main' : 'primary.main',
                          fontSize: 12
                        }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" fontWeight="medium">
                            {appointment.patient}
                          </Typography>
                          <Chip
                            label={appointment.status}
                            size="small"
                            color={appointment.status === 'completed' ? 'success' : 'primary'}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={`${appointment.time} - ${appointment.type}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>
        </Grid>

        {/* Pending Chats & Health Resources */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Pending Chats
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/doctor/chat')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {pendingChats.map((chat) => (
                  <ListItem
                    key={chat.id}
                    sx={{
                      px: 0,
                      py: 1,
                      cursor: 'pointer',
                      '&:hover': { bgcolor: 'action.hover' },
                      borderRadius: 1
                    }}
                    onClick={() => navigate('/doctor/chat')}
                  >
                    <ListItemIcon>
                      <Avatar sx={{ width: 40, height: 40, bgcolor: 'primary.light' }}>
                        {chat.patient.split(' ').map(n => n[0]).join('')}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" fontWeight="medium">
                            {chat.patient}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Badge badgeContent={chat.unread} color="error" />
                            <Typography variant="caption" color="text.secondary"
                            sx={{
                              pl: 1
                            }}
                            >
                              {chat.time}
                            </Typography>
                          </Box>
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {chat.lastMessage}
                        </Typography>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Health Resources
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/doctor/health-resources')}
                  sx={{ borderRadius: 2 }}
                >
                  Manage All
                </Button>
              </Box>
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {healthResources.map((resource) => (
                  <ListItem key={resource.id} sx={{ px: 0, py: 1 }}>
                    <ListItemIcon>
                      <ArticleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" fontWeight="medium">
                            {resource.title}
                          </Typography>
                          <Chip
                            label={resource.status}
                            size="small"
                            color={resource.status === 'published' ? 'success' : 'warning'}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={`${resource.category} • ${resource.date}`}
                    />
                  </ListItem>
                ))}
              </List>
              <Divider sx={{ my: 2 }} />
              <Button
                fullWidth
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigate('/doctor/health-resources/new')}
                sx={{ borderRadius: 2 }}
              >
                Create New Resource
              </Button>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default DoctorDashboard;


