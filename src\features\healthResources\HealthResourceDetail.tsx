import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Chip, Divider, 
  Button, IconButton, Avatar, Grid, Breadcrumbs
} from '@mui/material';
import { 
  ArrowBack as ArrowBackIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Share as ShareIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { useNavigate, useParams, Link } from 'react-router-dom';
import Layout from '../../components/layout/Layout';

const HealthResourceDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [resource, setResource] = useState(null);
  const [isSaved, setIsSaved] = useState(false);
  
  useEffect(() => {
    // Check if resource is saved
    const savedResources = JSON.parse(localStorage.getItem('savedHealthResources') || '[]');
    setIsSaved(savedResources.includes(parseInt(id)));
    
    // Mock data fetch
    setTimeout(() => {
      // In a real app, you would fetch this data from an API
      const mockResource = {
        id: parseInt(id),
        title: 'Managing Exam Stress',
        category: 'Mental Health',
        author: 'Dr. Sarah Johnson',
        authorTitle: 'Clinical Psychologist',
        authorImage: 'https://source.unsplash.com/random/100x100/?portrait',
        publishDate: '2023-05-05',
        readTime: '5 min read',
        imageUrl: 'https://source.unsplash.com/random/1200x600/?stress',
        summary: 'Learn effective strategies to manage stress during exam periods and improve your mental wellbeing.',
        content: `
          <h2>Understanding Exam Stress</h2>
          <p>Exam stress is a normal response to the pressure of academic assessment. It's your body's way of preparing for a challenge, but when it becomes overwhelming, it can affect your performance and wellbeing.</p>
          
          <h2>Signs of Exam Stress</h2>
          <ul>
            <li>Difficulty sleeping or concentrating</li>
            <li>Increased heart rate and breathing</li>
            <li>Feeling irritable or on edge</li>
            <li>Loss of appetite or overeating</li>
            <li>Negative thoughts about performance</li>
          </ul>
          
          <h2>Effective Stress Management Strategies</h2>
          <p>Here are some evidence-based approaches to managing exam stress:</p>
          
          <h3>1. Create a Realistic Study Schedule</h3>
          <p>Break down your revision into manageable chunks and create a timetable that includes regular breaks. This helps prevent burnout and makes the workload feel more manageable.</p>
          
          <h3>2. Practice Mindfulness and Relaxation</h3>
          <p>Techniques such as deep breathing, progressive muscle relaxation, and mindfulness meditation can help reduce stress levels and improve focus.</p>
          
          <h3>3. Maintain Physical Wellbeing</h3>
          <p>Regular exercise, adequate sleep, and a balanced diet can significantly impact your stress levels and cognitive function.</p>
          
          <h3>4. Use Positive Self-Talk</h3>
          <p>Challenge negative thoughts about your abilities and replace them with realistic, positive statements. Remember that one exam doesn't define your worth or future.</p>
          
          <h3>5. Seek Support</h3>
          <p>Talk to friends, family, or university support services if you're feeling overwhelmed. Sometimes just verbalizing your concerns can provide relief.</p>
          
          <h2>When to Seek Professional Help</h2>
          <p>If stress is significantly impacting your daily functioning or mental health, consider speaking to a healthcare professional or university counseling service.</p>
        `,
        tags: ['stress management', 'mental health', 'exams', 'student wellbeing', 'anxiety']
      };
      
      setResource(mockResource);
      setLoading(false);
    }, 1000);
  }, [id]);
  
  const toggleSave = () => {
    const savedResources = JSON.parse(localStorage.getItem('savedHealthResources') || '[]');
    let updatedSaved;
    
    if (isSaved) {
      updatedSaved = savedResources.filter(resourceId => resourceId !== parseInt(id));
    } else {
      updatedSaved = [...savedResources, parseInt(id)];
    }
    
    localStorage.setItem('savedHealthResources', JSON.stringify(updatedSaved));
    setIsSaved(!isSaved);
  };
  
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography>Loading resource...</Typography>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 2 }}>
          <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
            Home
          </Link>
          <Link to="/health-resources" style={{ textDecoration: 'none', color: 'inherit' }}>
            Health Resources
          </Link>
          <Typography color="text.primary">{resource.title}</Typography>
        </Breadcrumbs>
        
        {/* Back Button */}
        <Button 
          startIcon={<ArrowBackIcon />} 
          onClick={() => navigate('/health-resources')}
          sx={{ mb: 3 }}
        >
          Back to Resources
        </Button>
        
        {/* Article Header */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            {resource.title}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Chip 
              label={resource.category} 
              color="primary" 
              size="small" 
              sx={{ mr: 2 }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
              {resource.publishDate}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {resource.readTime}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar 
                src={resource.authorImage}
                alt={resource.author}
                sx={{ mr: 2 }}
              />
              <Box>
                <Typography variant="subtitle1" fontWeight="medium">
                  {resource.author}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {resource.authorTitle}
                </Typography>
              </Box>
            </Box>
            
            <Box>
              <IconButton 
                onClick={toggleSave}
                color={isSaved ? 'primary' : 'default'}
                aria-label={isSaved ? 'Unsave resource' : 'Save resource'}
              >
                {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
              </IconButton>
              <IconButton aria-label="Share resource">
                <ShareIcon />
              </IconButton>
              <IconButton aria-label="Print resource">
                <PrintIcon />
              </IconButton>
            </Box>
          </Box>
        </Paper>
        
        {/* Featured Image */}
        <Box 
          sx={{ 
            mb: 4, 
            borderRadius: 3,
            overflow: 'hidden',
            height: { xs: '200px', sm: '300px', md: '400px' },
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <img 
            src={resource.imageUrl} 
            alt={resource.title}
            style={{ 
              width: '100%', 
              height: '100%', 
              objectFit: 'cover' 
            }}
          />
        </Box>
        
        {/* Article Content */}
        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 4, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="subtitle1" fontWeight="medium" paragraph>
                {resource.summary}
              </Typography>
              
              <Divider sx={{ my: 3 }} />
              
              <Box 
                sx={{ 
                  '& h2': {
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    mt: 4,
                    mb: 2
                  },
                  '& h3': {
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    mt: 3,
                    mb: 1.5
                  },
                  '& p': {
                    mb: 2,
                    lineHeight: 1.7
                  },
                  '& ul, & ol': {
                    pl: 4,
                    mb: 2
                  },
                  '& li': {
                    mb: 1
                  }
                }}
                dangerouslySetInnerHTML={{ __html: resource.content }}
              />
              
              <Divider sx={{ my: 3 }} />
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 3 }}>
                {resource.tags.map((tag, index) => (
                  <Chip 
                    key={index} 
                    label={tag} 
                    size="small" 
                    variant="outlined"
                    sx={{ mb: 1 }}
                  />
                ))}
              </Box>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                mb: 4, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Related Resources
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <img 
                    src="https://source.unsplash.com/random/100x100/?anxiety" 
                    alt="Anxiety Management"
                    style={{ 
                      width: 80, 
                      height: 80, 
                      objectFit: 'cover',
                      borderRadius: 8
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Anxiety Management Techniques
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dr. Emily Wilson
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <img 
                    src="https://source.unsplash.com/random/100x100/?meditation" 
                    alt="Meditation Basics"
                    style={{ 
                      width: 80, 
                      height: 80, 
                      objectFit: 'cover',
                      borderRadius: 8
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Meditation Basics for Beginners
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dr. Michael Chen
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <img 
                    src="https://source.unsplash.com/random/100x100/?sleep" 
                    alt="Sleep Hygiene"
                    style={{ 
                      width: 80, 
                      height: 80, 
                      objectFit: 'cover',
                      borderRadius: 8
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Improving Sleep Hygiene
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dr. Sarah Johnson
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Paper>
            
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Need Help?
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Typography variant="body2" paragraph>
                If you're experiencing mental health concerns, don't hesitate to reach out for support.
              </Typography>
              
              <Button 
                variant="contained" 
                color="primary" 
                fullWidth
                sx={{ mb: 2, borderRadius: 2 }}
                onClick={() => navigate('/appointments')}
              >
                Book an Appointment
              </Button>
              
              <Button 
                variant="outlined" 
                fullWidth
                sx={{ borderRadius: 2 }}
                onClick={() => navigate('/support-resources')}
              >
                View Support Resources
              </Button>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default HealthResourceDetail;
