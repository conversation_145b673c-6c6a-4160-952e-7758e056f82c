import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail,
  // Remove all type imports
} from 'firebase/auth';
import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  getDocs
} from 'firebase/firestore';
import { auth, db } from '../services/firebase';
import { generateStudentId } from '../services/studentIdService';
import { initializeProfileCompletion } from '../services/profileCompletionService';
import type { UserRole, ProfileCompletionStatus } from '../types/firebase';

// Use our custom User type
type User = {
  uid: string;
  email: string | null;
  displayName: string | null;
  role: UserRole;
  photoURL?: string | null;
};

interface FirebaseContextType {
  currentUser: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, role?: UserRole) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
  updateUserProfile: (data: { displayName?: string, photoURL?: string }) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const FirebaseContext = createContext<FirebaseContextType | undefined>(undefined);

export const useFirebase = () => {
  const context = useContext(FirebaseContext);
  if (context === undefined) {
    throw new Error('useFirebase must be used within a FirebaseProvider');
  }
  return context;
};

export const FirebaseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Get additional user data from Firestore
        try {
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            setCurrentUser({
              uid: firebaseUser.uid,
              email: firebaseUser.email,
              displayName: firebaseUser.displayName,
              role: userData.role || 'student',
              photoURL: firebaseUser.photoURL
            });
          } else {
            // If user document doesn't exist yet, use default values
            setCurrentUser({
              uid: firebaseUser.uid,
              email: firebaseUser.email,
              displayName: firebaseUser.displayName,
              role: 'student',
              photoURL: firebaseUser.photoURL
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          // Still set the user with basic info if Firestore fetch fails
          setCurrentUser({
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName,
            role: 'student',
            photoURL: firebaseUser.photoURL
          });
        }
      } else {
        setCurrentUser(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Assign doctors to a student
  const assignDoctorsToStudent = async (studentId: string) => {
    try {
      // Query for available doctors
      const doctorsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'doctor')
      );
      
      const doctorSnapshot = await getDocs(doctorsQuery);
      const availableDoctors = doctorSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // If we don't have enough real doctors, use some mock data
      const mockDoctors = [
        { id: 'mock1', name: 'Dr. Sarah Johnson', specialty: 'General Practice', department: 'Primary Care' },
        { id: 'mock2', name: 'Dr. Michael Chen', specialty: 'Mental Health', department: 'Counseling Services' },
        { id: 'mock3', name: 'Dr. Emily Rodriguez', specialty: 'Emergency Medicine', department: 'Emergency Care' },
        { id: 'mock4', name: 'Dr. David Kim', specialty: 'Internal Medicine', department: 'Internal Medicine' },
        { id: 'mock5', name: 'Dr. Lisa Thompson', specialty: 'Dermatology', department: 'Dermatology' }
      ];
      
      // Combine real and mock doctors if needed
      const allDoctors = availableDoctors.length >= 3 
        ? availableDoctors 
        : [...availableDoctors, ...mockDoctors.slice(0, 5 - availableDoctors.length)];
      
      // Assign at least 3 doctors to the student
      const assignedDoctors = allDoctors.slice(0, 5);
      
      // Save the assignments to Firestore
      await setDoc(doc(db, 'doctorAssignments', studentId), {
        doctors: assignedDoctors.map(doctor => ({
          id: doctor.id,
          name: doctor.name || doctor.displayName,
          specialty: doctor.specialty || 'General Practice',
          department: doctor.department || 'Primary Care',
          assignedDate: new Date().toISOString()
        })),
        updatedAt: new Date().toISOString()
      });
      
      return assignedDoctors;
    } catch (error) {
      console.error('Error assigning doctors:', error);
      throw error;
    }
  };

  // Register a new user
  const register = async (email: string, password: string, role: UserRole = 'student') => {
    try {
      setLoading(true);
      
      // Create the user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Set display name to email username
      const displayName = email.split('@')[0];
      await updateProfile(user, { displayName });
      
      // Generate student ID if this is a student
      let studentId: string | undefined;
      if (role === 'student') {
        studentId = await generateStudentId();
      }

      // Initialize profile completion status for students
      const profileCompletion: ProfileCompletionStatus | undefined = role === 'student' ? {
        isComplete: false,
        personalDetailsComplete: false,
        medicalInfoComplete: false,
        emergencyContactComplete: false,
        lastUpdated: new Date()
      } : undefined;

      // Create user document in Firestore
      const userData: any = {
        email,
        displayName,
        role,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add student-specific fields
      if (role === 'student') {
        userData.studentId = studentId;
        userData.profileCompletion = profileCompletion;
        userData.status = 'active';
      }

      await setDoc(doc(db, 'users', user.uid), userData);

      // If this is a student, assign doctors
      if (role === 'student') {
        await assignDoctorsToStudent(user.uid);
      }

      console.log(`✅ User registered successfully:`, {
        uid: user.uid,
        email,
        role,
        studentId: role === 'student' ? studentId : 'N/A'
      });
      
      // Update current user state
      setCurrentUser({
        uid: user.uid,
        email: user.email,
        displayName,
        role,
        photoURL: user.photoURL
      });
      
    } catch (error) {
      console.error('Error during registration:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Login user
  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      await signInWithEmailAndPassword(auth, email, password);
      // The auth state listener will update the currentUser
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = async () => {
    try {
      setLoading(true);
      await signOut(auth);
      // The auth state listener will update the currentUser
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update user profile
  const updateUserProfile = async (data: { displayName?: string, photoURL?: string }) => {
    try {
      if (!auth.currentUser) throw new Error('No user is signed in');
      
      await updateProfile(auth.currentUser, data);
      
      // Update Firestore document
      await setDoc(doc(db, 'users', auth.currentUser.uid), {
        ...data,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Update local state
      if (currentUser) {
        setCurrentUser({
          ...currentUser,
          displayName: data.displayName || currentUser.displayName,
          photoURL: data.photoURL || currentUser.photoURL
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
  };

  const value = {
    currentUser,
    login,
    register,
    logout,
    loading,
    updateUserProfile,
    resetPassword
  };

  return (
    <FirebaseContext.Provider value={value}>
      {children}
    </FirebaseContext.Provider>
  );
};







