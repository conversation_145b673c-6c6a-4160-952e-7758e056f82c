import { changeUserRole } from '../services/authService';

/**
 * Utility function to create a test admin user
 * This can be called from the browser console to make a user an admin
 */
export const makeUserAdmin = async (userId: string) => {
  try {
    await changeUserRole(userId, 'admin');
    console.log(`✅ User ${userId} has been made an admin!`);
    console.log('Please refresh the page to see the changes.');
    return true;
  } catch (error) {
    console.error('❌ Error making user admin:', error);
    return false;
  }
};

/**
 * Utility function to make a user a doctor
 */
export const makeUserDoctor = async (userId: string) => {
  try {
    await changeUserRole(userId, 'doctor');
    console.log(`✅ User ${userId} has been made a doctor!`);
    console.log('Please refresh the page to see the changes.');
    return true;
  } catch (error) {
    console.error('❌ Error making user doctor:', error);
    return false;
  }
};

/**
 * Utility function to make a user a student
 */
export const makeUserStudent = async (userId: string) => {
  try {
    await changeUserRole(userId, 'student');
    console.log(`✅ User ${userId} has been made a student!`);
    console.log('Please refresh the page to see the changes.');
    return true;
  } catch (error) {
    console.error('❌ Error making user student:', error);
    return false;
  }
};

// Make these functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).makeUserAdmin = makeUserAdmin;
  (window as any).makeUserDoctor = makeUserDoctor;
  (window as any).makeUserStudent = makeUserStudent;
}
