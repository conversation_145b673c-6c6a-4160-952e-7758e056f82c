import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Avatar,
  Chip,
  Alert
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { getAppointmentsByStudent, type Appointment } from '../services/appointmentService';

interface AppointmentReminderProps {
  checkInterval?: number; // Check interval in milliseconds (default: 30 seconds)
}

const AppointmentReminder: React.FC<AppointmentReminderProps> = ({ 
  checkInterval = 30000 // 30 seconds
}) => {
  const { currentUser, userProfile } = useAuth();
  const [upcomingAppointment, setUpcomingAppointment] = useState<Appointment | null>(null);
  const [showReminder, setShowReminder] = useState(false);
  const [timeUntilAppointment, setTimeUntilAppointment] = useState<string>('');

  // Check for upcoming appointments
  useEffect(() => {
    if (!currentUser?.uid || !userProfile || userProfile.role !== 'student') {
      return;
    }

    const checkUpcomingAppointments = async () => {
      try {
        const appointments = await getAppointmentsByStudent(currentUser.uid);
        const now = new Date();
        
        // Find appointments within the next 5 minutes
        const upcomingAppointments = appointments.filter(apt => {
          if (apt.status !== 'scheduled') return false;
          
          const appointmentDateTime = new Date(`${apt.date}T${apt.time}`);
          const timeDiff = appointmentDateTime.getTime() - now.getTime();
          const minutesUntil = timeDiff / (1000 * 60);
          
          // Show reminder if appointment is within 5 minutes
          return minutesUntil > 0 && minutesUntil <= 5;
        });

        if (upcomingAppointments.length > 0) {
          const nextAppointment = upcomingAppointments[0];
          const appointmentDateTime = new Date(`${nextAppointment.date}T${nextAppointment.time}`);
          const timeDiff = appointmentDateTime.getTime() - now.getTime();
          const minutesUntil = Math.ceil(timeDiff / (1000 * 60));
          
          setUpcomingAppointment(nextAppointment);
          setTimeUntilAppointment(`${minutesUntil} minute${minutesUntil !== 1 ? 's' : ''}`);
          setShowReminder(true);
          
          console.log(`⏰ Appointment reminder: ${nextAppointment.doctorName} in ${minutesUntil} minutes`);
        } else {
          setUpcomingAppointment(null);
          setShowReminder(false);
        }
      } catch (error) {
        console.error('Error checking upcoming appointments:', error);
      }
    };

    // Check immediately
    checkUpcomingAppointments();

    // Set up interval to check periodically
    const interval = setInterval(checkUpcomingAppointments, checkInterval);

    return () => clearInterval(interval);
  }, [currentUser?.uid, userProfile, checkInterval]);

  // Update countdown every minute
  useEffect(() => {
    if (!upcomingAppointment || !showReminder) return;

    const updateCountdown = () => {
      const now = new Date();
      const appointmentDateTime = new Date(`${upcomingAppointment.date}T${upcomingAppointment.time}`);
      const timeDiff = appointmentDateTime.getTime() - now.getTime();
      const minutesUntil = Math.ceil(timeDiff / (1000 * 60));
      
      if (minutesUntil <= 0) {
        setShowReminder(false);
        setUpcomingAppointment(null);
      } else {
        setTimeUntilAppointment(`${minutesUntil} minute${minutesUntil !== 1 ? 's' : ''}`);
      }
    };

    const countdownInterval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(countdownInterval);
  }, [upcomingAppointment, showReminder]);

  const handleDismiss = () => {
    setShowReminder(false);
  };

  const handleJoinAppointment = () => {
    // In a real app, this would navigate to the appointment or video call
    console.log('Joining appointment:', upcomingAppointment);
    setShowReminder(false);
    // You can add navigation logic here
    // navigate('/appointments/join/' + upcomingAppointment.id);
  };

  if (!showReminder || !upcomingAppointment) {
    return null;
  }

  return (
    <Dialog
      open={showReminder}
      onClose={handleDismiss}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <ScheduleIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6" fontWeight="bold">
            Appointment Reminder
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
          <Typography variant="body1" fontWeight="medium">
            Your appointment starts in {timeUntilAppointment}!
          </Typography>
        </Alert>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
            <PersonIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              {upcomingAppointment.doctorName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Healthcare Provider
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
            <Typography variant="body1">
              {new Date(upcomingAppointment.date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })} at {upcomingAppointment.time}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LocationIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
            <Typography variant="body1">
              Virtual Consultation
            </Typography>
          </Box>

          <Chip 
            label={upcomingAppointment.type || 'Consultation'} 
            color="primary" 
            variant="outlined"
            sx={{ textTransform: 'capitalize' }}
          />
        </Box>

        {upcomingAppointment.notes && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Notes:
            </Typography>
            <Typography variant="body2">
              {upcomingAppointment.notes}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button 
          onClick={handleDismiss} 
          variant="outlined"
          startIcon={<CloseIcon />}
          sx={{ borderRadius: 2 }}
        >
          Dismiss
        </Button>
        <Button 
          onClick={handleJoinAppointment} 
          variant="contained"
          sx={{ borderRadius: 2 }}
        >
          Join Appointment
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AppointmentReminder;
