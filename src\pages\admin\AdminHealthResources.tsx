import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Button, Grid, 
  Table, TableBody, TableCell, TableContainer, TableHead, 
  TableRow, IconButton, Chip, TextField, InputAdornment
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Search as SearchIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';

const AdminHealthResources = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [resources, setResources] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  
  useEffect(() => {
    // Mock data fetch
    setTimeout(() => {
      const mockResources = [
        { 
          id: 1, 
          title: 'Managing Exam Stress', 
          category: 'Mental Health', 
          author: '<PERSON>. <PERSON>',
          publishDate: '2023-05-05',
          status: 'Published'
        },
        { 
          id: 2, 
          title: 'Nutrition Tips for Students', 
          category: 'Nutrition', 
          author: 'Dr. <PERSON>',
          publishDate: '2023-05-03',
          status: 'Published'
        },
        { 
          id: 3, 
          title: 'Importance of Sleep', 
          category: 'Wellness', 
          author: 'Dr. Emily Wilson',
          publishDate: '2023-04-28',
          status: 'Published'
        },
        { 
          id: 4, 
          title: 'Seasonal Allergies Guide', 
          category: 'Health', 
          author: 'Dr. Robert Davis',
          publishDate: '2023-04-15',
          status: 'Draft'
        }
      ];
      
      setResources(mockResources);
      setLoading(false);
    }, 1000);
  }, []);
  
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };
  
  const filteredResources = resources.filter(resource => 
    resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.author.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Health Resources Management
          </Typography>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/admin/health-resources/new')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            Add New Resource
          </Button>
        </Box>
        
        {/* Search and Filters */}
        <Paper 
          sx={{ 
            p: 3, 
            mb: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search resources..."
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ borderRadius: 2 }}
              />
            </Grid>
          </Grid>
        </Paper>
        
        {/* Resources Table */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead sx={{ bgcolor: 'background.paper' }}>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Category</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Author</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Publish Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                      Loading resources...
                    </TableCell>
                  </TableRow>
                ) : filteredResources.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                      No resources found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredResources.map((resource) => (
                    <TableRow key={resource.id} hover>
                      <TableCell>{resource.title}</TableCell>
                      <TableCell>{resource.category}</TableCell>
                      <TableCell>{resource.author}</TableCell>
                      <TableCell>{resource.publishDate}</TableCell>
                      <TableCell>
                        <Chip 
                          label={resource.status} 
                          color={resource.status === 'Published' ? 'success' : 'default'} 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton 
                          size="small" 
                          color="primary"
                          onClick={() => navigate(`/admin/health-resources/${resource.id}`)}
                        >
                          <ViewIcon />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="primary"
                          onClick={() => navigate(`/admin/health-resources/${resource.id}/edit`)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminHealthResources;