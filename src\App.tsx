
import React, { useState, useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { CircularProgress, Box } from '@mui/material';
import theme from './theme';
import { FirebaseProvider } from './contexts/FirebaseContext';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { isMaintenanceMode, getSystemSettings } from './services/systemSettingsService';
import './utils/debugAuth'; // Import debug utilities

// Public Pages
import Home from './pages/Home';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import Unauthorized from './pages/auth/Unauthorized';
import NotFound from './pages/NotFound';
import SymptomChecker from './pages/SymptomChecker';
import Maintenance from './pages/Maintenance';
import AppointmentReminder from './components/AppointmentReminder';

// Student Features
import AppointmentsPage from './features/appointments/AppointmentsPage';
import HealthMetricsPage from './features/healthMetrics/HealthMetricsPage';
import FileManagementPage from './features/fileManagement/FileManagementPage';
import ChatPage from './features/communication/ChatPage';
import MedicationsPage from './features/medications/MedicationsPage';
import HealthResourcesPage from './features/healthResources/HealthResourcesPage';
import HealthResourceDetail from './features/healthResources/HealthResourceDetail';

// Profile Pages
import Profile from './pages/Profile';
import Dashboard from './pages/dashboard/Dashboard';

// Admin
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminUsers from './pages/admin/AdminUsers';
import AdminUserDetail from './pages/admin/AdminUserDetail';
import AdminUserEdit from './pages/admin/AdminUserEdit';
import AdminUserCreate from './pages/admin/AdminUserCreate';
import AdminDoctors from './pages/admin/AdminDoctors';
import AdminDoctorDetail from './pages/admin/AdminDoctorDetail';
import AdminDoctorEdit from './pages/admin/AdminDoctorEdit';
import AdminDoctorCreate from './pages/admin/AdminDoctorCreate';
import AdminStudents from './pages/admin/AdminStudents';
import AdminStudentDetail from './pages/admin/AdminStudentDetail';
import AdminStudentEdit from './pages/admin/AdminStudentEdit';
import AdminStudentCreate from './pages/admin/AdminStudentCreate';
import AdminMedicalRecordsList from './pages/admin/AdminMedicalRecordsList';
import AdminMedicalRecordDetail from './pages/admin/AdminMedicalRecordDetail';
import AdminMedicalRecordEdit from './pages/admin/AdminMedicalRecordEdit';
import AdminMedicalRecordCreate from './pages/admin/AdminMedicalRecordCreate';
import AdminAppointments from './pages/admin/AdminAppointments';
import AdminAppointmentDetail from './pages/admin/AdminAppointmentDetail';
import AdminAppointmentCreate from './pages/admin/AdminAppointmentCreate';
import AdminReports from './pages/admin/AdminReports';
import AdminSettings from './pages/admin/AdminSettings';
import UserManagement from './pages/admin/UserManagement';

// Doctor Routes
import DoctorDashboard from './pages/doctor/DoctorDashboard';
import HealthResourceCreate from './pages/doctor/HealthResourceCreate';
import HealthResourceEdit from './pages/doctor/HealthResourceEdit';
import HealthResourcesManagement from './pages/doctor/HealthResourcesManagement';
import DoctorPatients from './pages/doctor/DoctorPatients';
import DoctorAppointments from './pages/doctor/DoctorAppointments';
import DoctorChat from './pages/doctor/DoctorChat';
import DoctorProfile from './pages/doctor/DoctorProfile';
import ConditionManagement from './pages/admin/ConditionManagement';

function App() {
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkMaintenanceMode = async () => {
      try {
        const settings = await getSystemSettings();
        setMaintenanceMode(settings.maintenanceMode);
        setMaintenanceMessage(settings.maintenanceMessage);
      } catch (error) {
        console.error('Error checking maintenance mode:', error);
      } finally {
        setLoading(false);
      }
    };

    checkMaintenanceMode();
  }, []);

  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          <CircularProgress />
        </Box>
      </ThemeProvider>
    );
  }

  if (maintenanceMode) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Maintenance message={maintenanceMessage} />
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <FirebaseProvider>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/unauthorized" element={<Unauthorized />} />
              <Route path="/symptom-checker" element={<SymptomChecker />} />
              
              {/* Student Routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route 
                path="/appointments" 
                element={
                  <ProtectedRoute allowedRoles={['student']}>
                    <AppointmentsPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/health-metrics" 
                element={
                  <ProtectedRoute allowedRoles={['student']}>
                    <HealthMetricsPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/medical-records" 
                element={
                  <ProtectedRoute allowedRoles={['student']}>
                    <FileManagementPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/chat" 
                element={
                  <ProtectedRoute allowedRoles={['student']}>
                    <ChatPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/medications" 
                element={
                  <ProtectedRoute allowedRoles={['student']}>
                    <MedicationsPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/health-resources" 
                element={
                  <ProtectedRoute allowedRoles={['student', 'doctor']}>
                    <HealthResourcesPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/health-resources/:id" 
                element={
                  <ProtectedRoute allowedRoles={['student', 'doctor']}>
                    <HealthResourceDetail />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/profile" 
                element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                } 
              />
              
              {/* Doctor Routes */}
              <Route 
                path="/doctor/dashboard" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <DoctorDashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/patients" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <DoctorPatients />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/appointments" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <DoctorAppointments />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/chat" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <DoctorChat />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/profile" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <DoctorProfile />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/health-resources" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <HealthResourcesManagement />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/health-resources/new" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <HealthResourceCreate />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/doctor/health-resources/:id/edit" 
                element={
                  <ProtectedRoute allowedRoles={['doctor']}>
                    <HealthResourceEdit />
                  </ProtectedRoute>
                } 
              />
              
              {/* Admin Routes */}
              <Route 
                path="/admin/dashboard" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/users" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <UserManagement />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/users/:id" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminUserDetail />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/users/:id/edit" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminUserEdit />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/users/new" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminUserCreate />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/doctors" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDoctors />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/doctors/:id" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDoctorDetail />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/doctors/:id/edit" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDoctorEdit />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/doctors/new" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDoctorCreate />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/students" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminStudents />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/students/:id" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminStudentDetail />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/students/:id/edit" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminStudentEdit />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/students/new" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminStudentCreate />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/medical-records" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminMedicalRecordsList />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/medical-records/:id" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminMedicalRecordDetail />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/medical-records/:id/edit" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminMedicalRecordEdit />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/medical-records/new" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminMedicalRecordCreate />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/appointments" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminAppointments />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/appointments/:id" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminAppointmentDetail />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/appointments/new" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminAppointmentCreate />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/reports" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminReports />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/settings" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminSettings />
                  </ProtectedRoute>
                } 
              />
               <Route 
                path="/admin/conditions" 
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <ConditionManagement />
                  </ProtectedRoute>
                } 
              />
              
              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>

            {/* Global Appointment Reminder */}
            <AppointmentReminder />
          </Router>
        </AuthProvider>
      </FirebaseProvider>
    </ThemeProvider>
  );
}

export default App;








