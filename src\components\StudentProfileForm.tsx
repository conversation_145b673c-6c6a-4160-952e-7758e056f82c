// Student Profile Form Component
// Multi-step form for collecting comprehensive student profile information

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  LocalHospital as MedicalIcon,
  ContactPhone as ContactIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import { checkProfileCompletion, updateProfileCompletionStatus } from '../services/profileCompletionService';
import type { UserProfile, MedicalInformation, EmergencyContact } from '../types/firebase';

interface StudentProfileFormProps {
  open: boolean;
  onClose: () => void;
  initialStep?: number;
  isOverlay?: boolean;
}

const steps = ['Personal Details', 'Medical Information', 'Emergency Contact'];

const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
const genders = ['Male', 'Female', 'Other', 'Prefer not to say'];
const academicYears = ['Freshman', 'Sophomore', 'Junior', 'Senior', 'Graduate'];
const relationships = ['Parent', 'Guardian', 'Spouse', 'Sibling', 'Relative', 'Friend', 'Other'];

const StudentProfileForm: React.FC<StudentProfileFormProps> = ({
  open,
  onClose,
  initialStep = 0,
  isOverlay = false
}) => {
  const { userProfile, currentUser, refreshUserProfile } = useAuth();
  const [activeStep, setActiveStep] = useState(initialStep);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form data state
  const [formData, setFormData] = useState<Partial<UserProfile>>({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    phoneNumber: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    department: '',
    year: '',
    major: '',
    medicalInfo: {
      bloodType: '',
      allergies: [],
      medications: [],
      conditions: [],
      insuranceProvider: '',
      insurancePolicyNumber: '',
      primaryPhysician: '',
      primaryPhysicianPhone: '',
      medicalNotes: ''
    },
    emergencyContact: {
      name: '',
      relationship: '',
      phone: '',
      email: '',
      address: ''
    }
  });

  // Temporary arrays for adding/removing items
  const [newAllergy, setNewAllergy] = useState('');
  const [newMedication, setNewMedication] = useState('');
  const [newCondition, setNewCondition] = useState('');

  // Initialize form data from user profile
  useEffect(() => {
    if (userProfile) {
      setFormData({
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        dateOfBirth: userProfile.dateOfBirth || '',
        gender: userProfile.gender || '',
        phoneNumber: userProfile.phoneNumber || '',
        address: userProfile.address || '',
        city: userProfile.city || '',
        state: userProfile.state || '',
        zipCode: userProfile.zipCode || '',
        department: userProfile.department || '',
        year: userProfile.year || '',
        major: userProfile.major || '',
        medicalInfo: {
          bloodType: userProfile.medicalInfo?.bloodType || '',
          allergies: userProfile.medicalInfo?.allergies || [],
          medications: userProfile.medicalInfo?.medications || [],
          conditions: userProfile.medicalInfo?.conditions || [],
          insuranceProvider: userProfile.medicalInfo?.insuranceProvider || '',
          insurancePolicyNumber: userProfile.medicalInfo?.insurancePolicyNumber || '',
          primaryPhysician: userProfile.medicalInfo?.primaryPhysician || '',
          primaryPhysicianPhone: userProfile.medicalInfo?.primaryPhysicianPhone || '',
          medicalNotes: userProfile.medicalInfo?.medicalNotes || ''
        },
        emergencyContact: {
          name: userProfile.emergencyContact?.name || '',
          relationship: userProfile.emergencyContact?.relationship || '',
          phone: userProfile.emergencyContact?.phone || '',
          email: userProfile.emergencyContact?.email || '',
          address: userProfile.emergencyContact?.address || ''
        }
      });
    }
  }, [userProfile]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleMedicalInfoChange = (field: keyof MedicalInformation, value: any) => {
    setFormData(prev => ({
      ...prev,
      medicalInfo: {
        ...prev.medicalInfo!,
        [field]: value
      }
    }));
  };

  const handleEmergencyContactChange = (field: keyof EmergencyContact, value: any) => {
    setFormData(prev => ({
      ...prev,
      emergencyContact: {
        ...prev.emergencyContact!,
        [field]: value
      }
    }));
  };

  const addArrayItem = (arrayType: 'allergies' | 'medications' | 'conditions', value: string) => {
    if (!value.trim()) return;
    
    const currentArray = formData.medicalInfo?.[arrayType] || [];
    if (!currentArray.includes(value.trim())) {
      handleMedicalInfoChange(arrayType, [...currentArray, value.trim()]);
    }
    
    // Clear the input
    if (arrayType === 'allergies') setNewAllergy('');
    if (arrayType === 'medications') setNewMedication('');
    if (arrayType === 'conditions') setNewCondition('');
  };

  const removeArrayItem = (arrayType: 'allergies' | 'medications' | 'conditions', index: number) => {
    const currentArray = formData.medicalInfo?.[arrayType] || [];
    const newArray = currentArray.filter((_, i) => i !== index);
    handleMedicalInfoChange(arrayType, newArray);
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 0: // Personal Details
        return !!(
          formData.firstName &&
          formData.lastName &&
          formData.dateOfBirth &&
          formData.gender &&
          formData.phoneNumber &&
          formData.address &&
          formData.city &&
          formData.state &&
          formData.zipCode &&
          formData.department &&
          formData.year &&
          formData.major
        );
      case 1: // Medical Information
        return !!(formData.medicalInfo?.bloodType);
      case 2: // Emergency Contact
        return !!(
          formData.emergencyContact?.name &&
          formData.emergencyContact?.relationship &&
          formData.emergencyContact?.phone
        );
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!currentUser || !userProfile) return;

    try {
      setLoading(true);
      setError(null);

      // Update user profile in Firestore
      const userRef = doc(db, 'users', currentUser.uid);
      
      const updateData = {
        ...formData,
        updatedAt: new Date()
      };

      await updateDoc(userRef, updateData);

      // Check and update completion status
      const updatedProfile = { ...userProfile, ...formData } as UserProfile;
      const completionResult = checkProfileCompletion(updatedProfile);
      await updateProfileCompletionStatus(currentUser.uid, completionResult);

      // Refresh user profile
      await refreshUserProfile();

      setSuccess(true);
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (err: any) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return renderPersonalDetailsStep();
      case 1:
        return renderMedicalInfoStep();
      case 2:
        return renderEmergencyContactStep();
      default:
        return null;
    }
  };

  const renderPersonalDetailsStep = () => (
    <Grid container spacing={3} sx={{}}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Personal Information
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="First Name"
          value={formData.firstName || ''}
          onChange={(e) => handleInputChange('firstName', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="Last Name"
          value={formData.lastName || ''}
          onChange={(e) => handleInputChange('lastName', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="Date of Birth"
          type="date"
          value={formData.dateOfBirth || ''}
          onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Gender</InputLabel>
          <Select
            value={formData.gender || ''}
            onChange={(e) => handleInputChange('gender', e.target.value)}
            label="Gender"
          >
            {genders.map(gender => (
              <MenuItem key={gender} value={gender}>{gender}</MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="Phone Number"
          value={formData.phoneNumber || ''}
          onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          required
          label="Address"
          value={formData.address || ''}
          onChange={(e) => handleInputChange('address', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          required
          label="City"
          value={formData.city || ''}
          onChange={(e) => handleInputChange('city', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          required
          label="State"
          value={formData.state || ''}
          onChange={(e) => handleInputChange('state', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          required
          label="ZIP Code"
          value={formData.zipCode || ''}
          onChange={(e) => handleInputChange('zipCode', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="Department/School"
          value={formData.department || ''}
          onChange={(e) => handleInputChange('department', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Academic Year</InputLabel>
          <Select
            value={formData.year || ''}
            onChange={(e) => handleInputChange('year', e.target.value)}
            label="Academic Year"
          >
            {academicYears.map(year => (
              <MenuItem key={year} value={year}>{year}</MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          required
          label="Major/Field of Study"
          value={formData.major || ''}
          onChange={(e) => handleInputChange('major', e.target.value)}
        />
      </Grid>
    </Grid>
  );

  const renderMedicalInfoStep = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Medical Information
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Blood Type</InputLabel>
          <Select
            value={formData.medicalInfo?.bloodType || ''}
            onChange={(e) => handleMedicalInfoChange('bloodType', e.target.value)}
            label="Blood Type"
          >
            {bloodTypes.map(type => (
              <MenuItem key={type} value={type}>{type}</MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Insurance Provider"
          value={formData.medicalInfo?.insuranceProvider || ''}
          onChange={(e) => handleMedicalInfoChange('insuranceProvider', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Insurance Policy Number"
          value={formData.medicalInfo?.insurancePolicyNumber || ''}
          onChange={(e) => handleMedicalInfoChange('insurancePolicyNumber', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Primary Physician"
          value={formData.medicalInfo?.primaryPhysician || ''}
          onChange={(e) => handleMedicalInfoChange('primaryPhysician', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Primary Physician Phone"
          value={formData.medicalInfo?.primaryPhysicianPhone || ''}
          onChange={(e) => handleMedicalInfoChange('primaryPhysicianPhone', e.target.value)}
        />
      </Grid>

      {/* Allergies */}
      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Allergies
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            label="Add allergy"
            value={newAllergy}
            onChange={(e) => setNewAllergy(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addArrayItem('allergies', newAllergy)}
          />
          <IconButton onClick={() => addArrayItem('allergies', newAllergy)}>
            <AddIcon />
          </IconButton>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {formData.medicalInfo?.allergies?.map((allergy, index) => (
            <Chip
              key={index}
              label={allergy}
              onDelete={() => removeArrayItem('allergies', index)}
              deleteIcon={<DeleteIcon />}
            />
          ))}
          {(!formData.medicalInfo?.allergies || formData.medicalInfo.allergies.length === 0) && (
            <Typography variant="body2" color="text.secondary">
              No allergies added. Click "Add allergy" if you have any allergies.
            </Typography>
          )}
        </Box>
      </Grid>

      {/* Medications */}
      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Current Medications
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            label="Add medication"
            value={newMedication}
            onChange={(e) => setNewMedication(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addArrayItem('medications', newMedication)}
          />
          <IconButton onClick={() => addArrayItem('medications', newMedication)}>
            <AddIcon />
          </IconButton>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {formData.medicalInfo?.medications?.map((medication, index) => (
            <Chip
              key={index}
              label={medication}
              onDelete={() => removeArrayItem('medications', index)}
              deleteIcon={<DeleteIcon />}
            />
          ))}
          {(!formData.medicalInfo?.medications || formData.medicalInfo.medications.length === 0) && (
            <Typography variant="body2" color="text.secondary">
              No medications added. Click "Add medication" if you take any medications.
            </Typography>
          )}
        </Box>
      </Grid>

      {/* Medical Conditions */}
      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Medical Conditions
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            label="Add medical condition"
            value={newCondition}
            onChange={(e) => setNewCondition(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addArrayItem('conditions', newCondition)}
          />
          <IconButton onClick={() => addArrayItem('conditions', newCondition)}>
            <AddIcon />
          </IconButton>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {formData.medicalInfo?.conditions?.map((condition, index) => (
            <Chip
              key={index}
              label={condition}
              onDelete={() => removeArrayItem('conditions', index)}
              deleteIcon={<DeleteIcon />}
            />
          ))}
          {(!formData.medicalInfo?.conditions || formData.medicalInfo.conditions.length === 0) && (
            <Typography variant="body2" color="text.secondary">
              No conditions added. Click "Add medical condition" if you have any medical conditions.
            </Typography>
          )}
        </Box>
      </Grid>

      <Grid item xs={12}>
        <TextField
          fullWidth
          multiline
          rows={3}
          label="Additional Medical Notes"
          value={formData.medicalInfo?.medicalNotes || ''}
          onChange={(e) => handleMedicalInfoChange('medicalNotes', e.target.value)}
          placeholder="Any additional medical information you'd like to share..."
        />
      </Grid>
    </Grid>
  );

  const renderEmergencyContactStep = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Emergency Contact Information
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Please provide details for someone we can contact in case of an emergency.
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="Full Name"
          value={formData.emergencyContact?.name || ''}
          onChange={(e) => handleEmergencyContactChange('name', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Relationship</InputLabel>
          <Select
            value={formData.emergencyContact?.relationship || ''}
            onChange={(e) => handleEmergencyContactChange('relationship', e.target.value)}
            label="Relationship"
          >
            {relationships.map(relationship => (
              <MenuItem key={relationship} value={relationship}>{relationship}</MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          required
          label="Phone Number"
          value={formData.emergencyContact?.phone || ''}
          onChange={(e) => handleEmergencyContactChange('phone', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Email Address"
          type="email"
          value={formData.emergencyContact?.email || ''}
          onChange={(e) => handleEmergencyContactChange('email', e.target.value)}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Address"
          value={formData.emergencyContact?.address || ''}
          onChange={(e) => handleEmergencyContactChange('address', e.target.value)}
          placeholder="Street address, city, state, ZIP code"
        />
      </Grid>
    </Grid>
  );

  return (
    <Dialog
      open={open}
      onClose={isOverlay ? undefined : onClose}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={isOverlay}
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '600px',
          
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h5" fontWeight="bold">
          {isOverlay ? 'Complete Your Profile' : 'Edit Profile'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {isOverlay
            ? 'Please complete all sections to access the platform'
            : 'Update your profile information'
          }
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ px: 3 }}>
        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Success Alert */}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Profile updated successfully!
          </Alert>
        )}

        {/* Step Content */}
        <Paper sx={{ p: 3, minHeight: '400px' boxShadow: '0 4px 20px rgba(0,0,0,0.08)'}}>
          {renderStepContent(activeStep)}
        </Paper>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          onClick={handleBack}
          disabled={activeStep === 0 || loading}
        >
          Back
        </Button>
        <Box sx={{ flex: 1 }} />
        {activeStep < steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={!validateStep(activeStep) || loading}
          >
            Next
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={!validateStep(activeStep) || loading}
            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {loading ? 'Saving...' : 'Save Profile'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default StudentProfileForm;
