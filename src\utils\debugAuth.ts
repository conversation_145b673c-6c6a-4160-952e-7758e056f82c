import { auth, db } from '../services/firebase';
import { doc, getDoc } from 'firebase/firestore';

/**
 * Debug utility to check current user authentication and role
 * Call this from browser console: window.debugAuth()
 */
export const debugAuth = async () => {
  console.log('🔍 === AUTH DEBUG INFO ===');
  
  // Check Firebase Auth user
  const currentUser = auth.currentUser;
  console.log('Firebase Auth User:', {
    uid: currentUser?.uid,
    email: currentUser?.email,
    displayName: currentUser?.displayName,
    emailVerified: currentUser?.emailVerified
  });

  if (!currentUser) {
    console.log('❌ No user is currently logged in');
    return;
  }

  try {
    // Check Firestore user document
    const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('✅ Firestore User Document:', {
        uid: currentUser.uid,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role,
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt
      });
      
      console.log('🎯 Current Role:', userData.role);
      
      // Check expected redirect
      switch (userData.role) {
        case 'admin':
          console.log('📍 Should redirect to: /admin/dashboard');
          break;
        case 'doctor':
          console.log('📍 Should redirect to: /doctor/dashboard');
          break;
        case 'student':
          console.log('📍 Should redirect to: /dashboard');
          break;
        default:
          console.log('📍 Unknown role, should redirect to: /dashboard');
      }
    } else {
      console.log('❌ No Firestore document found for user');
    }
  } catch (error) {
    console.error('❌ Error fetching user data:', error);
  }
  
  console.log('🔍 === END DEBUG INFO ===');
};

/**
 * Force refresh user profile data
 */
export const forceRefreshProfile = async () => {
  const currentUser = auth.currentUser;
  if (!currentUser) {
    console.log('❌ No user logged in');
    return;
  }

  try {
    const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('✅ Fresh user data from Firestore:', userData);
      console.log('🔄 Please refresh the page to see changes');
    }
  } catch (error) {
    console.error('❌ Error refreshing profile:', error);
  }
};

// Make functions available globally for debugging
declare global {
  interface Window {
    debugAuth: () => Promise<void>;
    forceRefreshProfile: () => Promise<void>;
  }
}

if (typeof window !== 'undefined') {
  window.debugAuth = debugAuth;
  window.forceRefreshProfile = forceRefreshProfile;
}
