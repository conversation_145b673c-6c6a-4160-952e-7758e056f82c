import React, { useState } from 'react';
import { 
  Box, 
  ImageList, 
  ImageListItem, 
  ImageListItemBar, 
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
  Button,
  Chip,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import ZoomInIcon from '@mui/icons-material/ZoomIn';

interface MedicalImage {
  id: number;
  title: string;
  date: string;
  imageUrl: string;
  description: string;
  type: string;
}

interface MedicalRecordImagesProps {
  images: MedicalImage[];
  variant?: 'standard' | 'quilted' | 'grid';
  cols?: number;
  onDelete?: (id: number) => void;
}

const MedicalRecordImages: React.FC<MedicalRecordImagesProps> = ({
  images,
  variant = 'standard',
  cols = 3,
  onDelete
}) => {
  const [selectedImage, setSelectedImage] = useState<MedicalImage | null>(null);
  
  const handleOpenImage = (image: MedicalImage) => {
    setSelectedImage(image);
  };
  
  const handleCloseImage = () => {
    setSelectedImage(null);
  };
  
  const handleDeleteImage = (id: number) => {
    if (onDelete) {
      onDelete(id);
    }
  };
  
  // Function to determine column span for quilted layout
  const getQuiltedCols = (index: number) => {
    if (index % 3 === 0) return 2;
    return 1;
  };
  
  // Function to determine row span for quilted layout
  const getQuiltedRows = (index: number) => {
    if (index % 4 === 0) return 2;
    return 1;
  };
  
  if (variant === 'quilted') {
    return (
      <>
        <ImageList
          variant="quilted"
          cols={cols}
          gap={16}
        >
          {images.map((image, index) => (
            <ImageListItem 
              key={image.id} 
              cols={getQuiltedCols(index)} 
              rows={getQuiltedRows(index)}
              sx={{ 
                cursor: 'pointer',
                borderRadius: 2,
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                  '& .MuiImageListItemBar-root': {
                    opacity: 1
                  }
                }
              }}
              onClick={() => handleOpenImage(image)}
            >
              <img
                src={image.imageUrl}
                alt={image.title}
                loading="lazy"
                style={{ height: '100%', objectFit: 'cover' }}
              />
              <ImageListItemBar
                title={image.title}
                subtitle={image.date}
                sx={{ 
                  opacity: 0.8,
                  transition: 'opacity 0.3s'
                }}
                actionIcon={
                  <IconButton
                    sx={{ color: 'white' }}
                    aria-label={`info about ${image.title}`}
                  >
                    <ZoomInIcon />
                  </IconButton>
                }
              />
            </ImageListItem>
          ))}
        </ImageList>
        
        {/* Image Detail Dialog */}
        <Dialog
          open={Boolean(selectedImage)}
          onClose={handleCloseImage}
          maxWidth="md"
          fullWidth
        >
          {selectedImage && (
            <>
              <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">{selectedImage.title}</Typography>
                <IconButton onClick={handleCloseImage} size="small">
                  <CloseIcon />
                </IconButton>
              </DialogTitle>
              <DialogContent>
                <Box sx={{ mb: 2 }}>
                  <img
                    src={selectedImage.imageUrl}
                    alt={selectedImage.title}
                    style={{ width: '100%', maxHeight: '70vh', objectFit: 'contain' }}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">
                    Date: {selectedImage.date}
                  </Typography>
                  <Chip label={selectedImage.type} color="primary" size="small" />
                </Box>
                <Typography variant="body1" gutterBottom>
                  {selectedImage.description}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button 
                    startIcon={<DownloadIcon />} 
                    sx={{ mr: 1 }}
                  >
                    Download
                  </Button>
                  {onDelete && (
                    <Button 
                      color="error" 
                      startIcon={<DeleteIcon />}
                      onClick={() => {
                        handleDeleteImage(selectedImage.id);
                        handleCloseImage();
                      }}
                    >
                      Delete
                    </Button>
                  )}
                </Box>
              </DialogContent>
            </>
          )}
        </Dialog>
      </>
    );
  }
  
  if (variant === 'grid') {
    return (
      <>
        <Grid container spacing={2}>
          {images.map((image) => (
            <Grid item xs={12} sm={6} md={4} key={image.id}>
              <Card 
                sx={{ 
                  borderRadius: 2,
                  overflow: 'hidden',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  '&:hover': {
                    boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
                  }
                }}
              >
                <CardMedia
                  component="img"
                  height="180"
                  image={image.imageUrl}
                  alt={image.title}
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleOpenImage(image)}
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle1" component="div" fontWeight="medium">
                    {image.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {image.date}
                  </Typography>
                  <Chip 
                    label={image.type} 
                    size="small" 
                    sx={{ mt: 1 }} 
                    color="primary" 
                    variant="outlined" 
                  />
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    startIcon={<ZoomInIcon />}
                    onClick={() => handleOpenImage(image)}
                  >
                    View
                  </Button>
                  {onDelete && (
                    <Button 
                      size="small" 
                      color="error" 
                      startIcon={<DeleteIcon />}
                      onClick={() => handleDeleteImage(image.id)}
                    >
                      Delete
                    </Button>
                  )}
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        {/* Image Detail Dialog */}
        <Dialog
          open={Boolean(selectedImage)}
          onClose={handleCloseImage}
          maxWidth="md"
          fullWidth
        >
          {selectedImage && (
            <>
              <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">{selectedImage.title}</Typography>
                <IconButton onClick={handleCloseImage} size="small">
                  <CloseIcon />
                </IconButton>
              </DialogTitle>
              <DialogContent>
                <Box sx={{ mb: 2 }}>
                  <img
                    src={selectedImage.imageUrl}
                    alt={selectedImage.title}
                    style={{ width: '100%', maxHeight: '70vh', objectFit: 'contain' }}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">
                    Date: {selectedImage.date}
                  </Typography>
                  <Chip label={selectedImage.type} color="primary" size="small" />
                </Box>
                <Typography variant="body1" gutterBottom>
                  {selectedImage.description}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button 
                    startIcon={<DownloadIcon />} 
                    sx={{ mr: 1 }}
                  >
                    Download
                  </Button>
                  {onDelete && (
                    <Button 
                      color="error" 
                      startIcon={<DeleteIcon />}
                      onClick={() => {
                        handleDeleteImage(selectedImage.id);
                        handleCloseImage();
                      }}
                    >
                      Delete
                    </Button>
                  )}
                </Box>
              </DialogContent>
            </>
          )}
        </Dialog>
      </>
    );
  }
  
  // Default standard layout
  return (
    <>
      <ImageList cols={cols} gap={16}>
        {images.map((image) => (
          <ImageListItem 
            key={image.id}
            sx={{ 
              cursor: 'pointer',
              borderRadius: 2,
              overflow: 'hidden',
              '&:hover': {
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                '& .MuiImageListItemBar-root': {
                  opacity: 1
                }
              }
            }}
            onClick={() => handleOpenImage(image)}
          >
            <img
              src={image.imageUrl}
              alt={image.title}
              loading="lazy"
              style={{ height: '100%', objectFit: 'cover' }}
            />
            <ImageListItemBar
              title={image.title}
              subtitle={image.date}
              sx={{ 
                opacity: 0.8,
                transition: 'opacity 0.3s'
              }}
              actionIcon={
                <IconButton
                  sx={{ color: 'white' }}
                  aria-label={`info about ${image.title}`}
                >
                  <InfoIcon />
                </IconButton>
              }
            />
          </ImageListItem>
        ))}
      </ImageList>
      
      {/* Image Detail Dialog */}
      <Dialog
        open={Boolean(selectedImage)}
        onClose={handleCloseImage}
        maxWidth="md"
        fullWidth
      >
        {selectedImage && (
          <>
            <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">{selectedImage.title}</Typography>
              <IconButton onClick={handleCloseImage} size="small">
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mb: 2 }}>
                <img
                  src={selectedImage.imageUrl}
                  alt={selectedImage.title}
                  style={{ width: '100%', maxHeight: '70vh', objectFit: 'contain' }}
                />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="subtitle1" color="text.secondary">
                  Date: {selectedImage.date}
                </Typography>
                <Chip label={selectedImage.type} color="primary" size="small" />
              </Box>
              <Typography variant="body1" gutterBottom>
                {selectedImage.description}
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button 
                  startIcon={<DownloadIcon />} 
                  sx={{ mr: 1 }}
                >
                  Download
                </Button>
                {onDelete && (
                  <Button 
                    color="error" 
                    startIcon={<DeleteIcon />}
                    onClick={() => {
                      handleDeleteImage(selectedImage.id);
                      handleCloseImage();
                    }}
                  >
                    Delete
                  </Button>
                )}
              </Box>
            </DialogContent>
          </>
        )}
      </Dialog>
    </>
  );
};

export default MedicalRecordImages;
