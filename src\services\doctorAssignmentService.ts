// Doctor Assignment Service
// Manages the assignment and retrieval of doctors for students

import { collection, getDocs, doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from './firebase';
import { getAllUsers } from './authService';
import type { UserProfile } from '../types/firebase';

export interface Doctor {
  id: string;
  name: string;
  specialty: string;
  department: string;
  avatar?: string;
  online?: boolean;
  assigned?: boolean;
  requested?: boolean;
  email?: string;
  uid?: string;
}

export interface AssignedDoctor extends Doctor {
  assignedDate: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

// Mock database of available doctors
const AVAILABLE_DOCTORS: Doctor[] = [
  { id: 1, name: 'Dr. <PERSON>', specialty: 'General Practice', department: 'Primary Care', avatar: '', online: true },
  { id: 2, name: 'Dr. <PERSON>', specialty: 'Mental Health', department: 'Counseling Services', avatar: '', online: true },
  { id: 3, name: 'Dr. <PERSON>', specialty: 'Emergency Medicine', department: 'Emergency Care', avatar: '', online: false },
  { id: 4, name: 'Dr. <PERSON>', specialty: 'Internal Medicine', department: 'Internal Medicine', avatar: '', online: true },
  { id: 5, name: 'Dr. Lisa Thompson', specialty: 'Dermatology', department: 'Dermatology', avatar: '', online: false },
  { id: 6, name: 'Dr. James Wilson', specialty: 'Cardiology', department: 'Cardiology', avatar: '', online: true },
  { id: 7, name: 'Dr. Maria Garcia', specialty: 'Gynecology', department: 'Women\'s Health', avatar: '', online: true },
  { id: 8, name: 'Dr. Robert Brown', specialty: 'Orthopedics', department: 'Orthopedics', avatar: '', online: false },
  { id: 9, name: 'Dr. Jennifer Lee', specialty: 'Psychiatry', department: 'Mental Health', avatar: '', online: true },
  { id: 10, name: 'Dr. Thomas Anderson', specialty: 'Neurology', department: 'Neurology', avatar: '', online: false },
  { id: 11, name: 'Dr. Amanda White', specialty: 'Pediatrics', department: 'Pediatrics', avatar: '', online: true },
  { id: 12, name: 'Dr. Kevin Martinez', specialty: 'Ophthalmology', department: 'Eye Care', avatar: '', online: true },
  { id: 13, name: 'Dr. Rachel Davis', specialty: 'Endocrinology', department: 'Endocrinology', avatar: '', online: false },
  { id: 14, name: 'Dr. Steven Clark', specialty: 'Urology', department: 'Urology', avatar: '', online: true },
  { id: 15, name: 'Dr. Nicole Taylor', specialty: 'Rheumatology', department: 'Rheumatology', avatar: '', online: false }
];

// Core specialties that every student should have access to
const CORE_SPECIALTIES = ['General Practice', 'Mental Health', 'Emergency Medicine'];

/**
 * Get all doctors from Firebase
 */
export const getDoctorsFromFirebase = async (): Promise<Doctor[]> => {
  try {
    const allUsers = await getAllUsers();
    const doctors = allUsers
      .filter(user => user.role === 'doctor')
      .map(user => ({
        id: user.uid,
        uid: user.uid,
        name: user.displayName || 'Unknown Doctor',
        specialty: user.specialty || 'General Practice',
        department: user.department || 'Primary Care',
        email: user.email,
        avatar: '',
        online: Math.random() > 0.3, // Random online status for demo
        assigned: false,
        requested: false
      }));

    console.log(`📋 Found ${doctors.length} doctors in Firebase`);
    return doctors;
  } catch (error) {
    console.error('Error fetching doctors from Firebase:', error);
    // Return mock doctors as fallback
    return AVAILABLE_DOCTORS.map(doctor => ({
      ...doctor,
      id: doctor.id.toString(),
      uid: doctor.id.toString()
    }));
  }
};

/**
 * Smart doctor assignment algorithm
 * Ensures every student gets essential care coverage
 */
export const assignDoctorsToStudent = async (studentId: string): Promise<AssignedDoctor[]> => {
  console.log(`🏥 Starting doctor assignment for student: ${studentId}`);

  try {
    // Get real doctors from Firebase
    const availableDoctors = await getDoctorsFromFirebase();

    if (availableDoctors.length === 0) {
      console.log('⚠️ No doctors available in database - student will have empty doctor list');
      return [];
    }

    console.log(`👨‍⚕️ Found ${availableDoctors.length} available doctors`);

    // If we have fewer than 5 doctors total, assign all of them
    if (availableDoctors.length <= 5) {
      const assignedDoctors = formatAssignedDoctors(availableDoctors);
      console.log(`✅ Assigned all ${assignedDoctors.length} available doctors (less than 5 total)`);
      await saveAssignedDoctors(studentId, assignedDoctors);
      return assignedDoctors;
    }

    // Load balancing: Get current assignment counts for each doctor
    const doctorAssignmentCounts = await getDoctorAssignmentCounts(availableDoctors);

    // Step 1: Assign core specialists (essential for all students)
    const coreAssignments = CORE_SPECIALTIES.map(specialty => {
      const specialistDoctors = availableDoctors.filter(d => d.specialty === specialty);
      if (specialistDoctors.length > 0) {
        // Load balancing: pick the doctor with the least assignments
        return specialistDoctors.reduce((least, current) =>
          doctorAssignmentCounts[current.id] < doctorAssignmentCounts[least.id] ? current : least
        );
      }
      return null;
    }).filter(Boolean);

    // Step 2: Assign additional doctors for comprehensive care
    const remainingDoctors = availableDoctors.filter(d =>
      !coreAssignments.some(core => core && core.id === d.id)
    );

    const additionalCount = Math.max(0, 5 - coreAssignments.length); // Aim for 5 total doctors

    // Sort remaining doctors by assignment count (ascending) for load balancing
    const sortedRemainingDoctors = remainingDoctors.sort((a, b) =>
      doctorAssignmentCounts[a.id] - doctorAssignmentCounts[b.id]
    );

    const additionalDoctors = sortedRemainingDoctors.slice(0, additionalCount);

    // Step 3: Combine assignments
    const allAssignments = [...coreAssignments, ...additionalDoctors].filter(Boolean);
  
    // Step 4: Format and save assignments
    const assignedDoctors = formatAssignedDoctors(allAssignments);

    console.log(`✅ Successfully assigned ${assignedDoctors.length} doctors with load balancing:`);
    assignedDoctors.forEach(doctor => {
      console.log(`   • ${doctor.name} (${doctor.specialty}) - Previous assignments: ${doctorAssignmentCounts[doctor.id]}`);
    });

    // Step 5: Save to Firestore
    await saveAssignedDoctors(studentId, assignedDoctors);

    return assignedDoctors;

  } catch (error) {
    console.error('Error assigning doctors:', error);
    return [];
  }
};

/**
 * Format doctors as AssignedDoctor objects with realistic chat data
 */
const formatAssignedDoctors = (doctors: Doctor[]): AssignedDoctor[] => {
  const mockChatData = [
    {
      lastMessage: 'Your test results came back normal. Let\'s schedule a follow-up next week.',
      lastMessageTime: '2 min ago',
      unreadCount: 2
    },
    {
      lastMessage: 'How are you feeling after starting the new medication?',
      lastMessageTime: '15 min ago',
      unreadCount: 1
    },
    {
      lastMessage: 'Remember to take your medication with food. Any side effects?',
      lastMessageTime: '1 hour ago',
      unreadCount: 0
    },
    {
      lastMessage: 'Your appointment is confirmed for tomorrow at 2 PM.',
      lastMessageTime: '3 hours ago',
      unreadCount: 3
    },
    {
      lastMessage: 'I\'ve updated your treatment plan. Please review the attached document.',
      lastMessageTime: '1 day ago',
      unreadCount: 0
    }
  ];

  return doctors.map((doctor, index) => ({
    ...doctor,
    assignedDate: new Date().toISOString(),
    lastMessage: mockChatData[index]?.lastMessage || 'Welcome! I\'m here to help with your healthcare needs.',
    lastMessageTime: mockChatData[index]?.lastMessageTime || 'Just now',
    unreadCount: mockChatData[index]?.unreadCount || 0,
    assigned: true
  }));
};

/**
 * Save assigned doctors to Firestore
 */
const saveAssignedDoctors = async (studentId: string, doctors: AssignedDoctor[]): Promise<void> => {
  try {
    await setDoc(doc(db, 'doctorAssignments', studentId), {
      doctors: doctors.map(doctor => ({
        id: doctor.id,
        uid: doctor.uid,
        name: doctor.name,
        specialty: doctor.specialty,
        department: doctor.department,
        email: doctor.email,
        assignedDate: doctor.assignedDate,
        lastMessage: doctor.lastMessage,
        lastMessageTime: doctor.lastMessageTime,
        unreadCount: doctor.unreadCount
      })),
      updatedAt: new Date().toISOString()
    });
    console.log(`💾 Saved doctor assignments to Firestore for student: ${studentId}`);
  } catch (error) {
    console.error('Error saving doctor assignments:', error);
    // Fallback to localStorage
    localStorage.setItem(`assignedDoctors_${studentId}`, JSON.stringify(doctors));
  }
};

/**
 * Get assigned doctors for a student
 */
export const getAssignedDoctors = async (studentId: string): Promise<AssignedDoctor[]> => {
  try {
    const docRef = doc(db, 'doctorAssignments', studentId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return data.doctors || [];
    }

    // Fallback to localStorage
    const stored = localStorage.getItem(`assignedDoctors_${studentId}`);
    if (stored) {
      return JSON.parse(stored);
    }

    return [];
  } catch (error) {
    console.error('Error fetching assigned doctors:', error);
    // Fallback to localStorage
    const stored = localStorage.getItem(`assignedDoctors_${studentId}`);
    if (stored) {
      return JSON.parse(stored);
    }
    return [];
  }
};

/**
 * Get doctor assignment counts for load balancing
 */
const getDoctorAssignmentCounts = async (doctors: Doctor[]): Promise<Record<string, number>> => {
  try {
    const assignmentCounts: Record<string, number> = {};

    // Initialize all doctors with 0 assignments
    doctors.forEach(doctor => {
      assignmentCounts[doctor.id] = 0;
    });

    // Query all doctor assignments to count current assignments
    const assignmentsSnapshot = await getDocs(collection(db, 'doctorAssignments'));

    assignmentsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.doctors && Array.isArray(data.doctors)) {
        data.doctors.forEach((assignedDoctor: any) => {
          if (assignmentCounts.hasOwnProperty(assignedDoctor.id)) {
            assignmentCounts[assignedDoctor.id]++;
          }
        });
      }
    });

    console.log('📊 Doctor assignment counts:', assignmentCounts);
    return assignmentCounts;
  } catch (error) {
    console.error('Error getting doctor assignment counts:', error);
    // Return empty counts if error
    const emptyCounts: Record<string, number> = {};
    doctors.forEach(doctor => {
      emptyCounts[doctor.id] = 0;
    });
    return emptyCounts;
  }
};

/**
 * Get all available doctors (for browsing)
 */
export const getAllDoctors = async (): Promise<Doctor[]> => {
  try {
    const doctors = await getDoctorsFromFirebase();
    return doctors.map(doctor => ({
      ...doctor,
      assigned: false,
      requested: false
    }));
  } catch (error) {
    console.error('Error fetching all doctors:', error);
    return [];
  }
};

/**
 * Request assignment of a new doctor
 */
export const requestDoctorAssignment = async (studentId: string, doctorId: number): Promise<boolean> => {
  console.log(`📋 Processing doctor assignment request: Student ${studentId} → Doctor ${doctorId}`);
  
  // In a real app, this would create a request in the database
  // For now, we'll simulate the request process
  
  const doctor = AVAILABLE_DOCTORS.find(d => d.id === doctorId);
  if (!doctor) {
    console.error('❌ Doctor not found');
    return false;
  }

  // Simulate approval process (in real app, this would be handled by admin/doctor)
  setTimeout(() => {
    console.log(`✅ Doctor assignment request approved: ${doctor.name}`);
    
    // Add to assigned doctors
    const currentAssigned = getAssignedDoctors(studentId);
    const newAssignment: AssignedDoctor = {
      ...doctor,
      assignedDate: new Date().toISOString(),
      lastMessage: `Hello! I'm ${doctor.name}. I'm now available to help you.`,
      lastMessageTime: 'Just now',
      unreadCount: 1,
      assigned: true
    };
    
    const updatedAssigned = [...currentAssigned, newAssignment];
    localStorage.setItem(`assignedDoctors_${studentId}`, JSON.stringify(updatedAssigned));
    
  }, 2000); // 2 second delay to simulate processing
  
  return true;
};

/**
 * Get assignment statistics for admin dashboard
 */
export const getAssignmentStats = () => {
  const totalDoctors = AVAILABLE_DOCTORS.length;
  const totalStudents = 150; // Mock number
  const avgAssignmentsPerStudent = 5;
  
  return {
    totalDoctors,
    totalStudents,
    avgAssignmentsPerStudent,
    totalAssignments: totalStudents * avgAssignmentsPerStudent,
    coreSpecialtiesCoverage: '100%', // All students get core specialties
    mostRequestedSpecialty: 'Mental Health',
    leastRequestedSpecialty: 'Rheumatology'
  };
};
