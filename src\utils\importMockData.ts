import { symptoms, conditions } from '../services/mockSymptomCheckerService';
import { importSymptomsFromMock, importConditionsFromMock } from '../services/symptomConditionService';

/**
 * Import all mock symptom checker data to Firebase
 */
export const importAllMockData = async (): Promise<void> => {
  try {
    console.log('🚀 Starting import of mock symptom checker data...');
    
    // Import symptoms
    console.log(`📋 Importing ${symptoms.length} symptoms...`);
    await importSymptomsFromMock(symptoms);
    
    // Import conditions
    console.log(`🏥 Importing ${conditions.length} conditions...`);
    await importConditionsFromMock(conditions);
    
    console.log('✅ Successfully imported all mock data!');
    console.log(`📊 Total imported: ${symptoms.length} symptoms, ${conditions.length} conditions`);
    
  } catch (error) {
    console.error('❌ Error importing mock data:', error);
    throw error;
  }
};

/**
 * Get import statistics
 */
export const getImportStats = () => {
  return {
    totalSymptoms: symptoms.length,
    totalConditions: conditions.length,
    symptomCategories: getSymptomCategories(),
    conditionTriageLevels: getConditionTriageLevels()
  };
};

/**
 * Get symptom categories from mock data
 */
const getSymptomCategories = () => {
  const categories = new Set<string>();
  
  // Categorize symptoms based on their ID ranges
  symptoms.forEach(symptom => {
    const id = parseInt(symptom.id.replace('s_', ''));
    
    if (id >= 1 && id <= 50) categories.add('General');
    else if (id >= 51 && id <= 70) categories.add('Respiratory');
    else if (id >= 71 && id <= 90) categories.add('Cardiovascular');
    else if (id >= 91 && id <= 110) categories.add('Gastrointestinal');
    else if (id >= 111 && id <= 130) categories.add('Neurological');
    else if (id >= 131 && id <= 150) categories.add('Musculoskeletal');
    else if (id >= 151 && id <= 170) categories.add('Dermatological');
    else if (id >= 171 && id <= 180) categories.add('Eye');
    else if (id >= 181 && id <= 190) categories.add('ENT');
    else if (id >= 191 && id <= 200) categories.add('Urinary');
    else if (id >= 201 && id <= 210) categories.add('Psychological');
    else if (id >= 211 && id <= 220) categories.add('Systemic');
  });
  
  return Array.from(categories);
};

/**
 * Get condition triage levels from mock data
 */
const getConditionTriageLevels = () => {
  const levels = new Map<string, number>();
  
  conditions.forEach(condition => {
    const level = condition.triage_level;
    levels.set(level, (levels.get(level) || 0) + 1);
  });
  
  return Object.fromEntries(levels);
};

/**
 * Validate symptom references in conditions
 */
export const validateSymptomReferences = () => {
  const symptomIds = new Set(symptoms.map(s => s.id));
  const invalidReferences: { conditionId: string; invalidSymptoms: string[] }[] = [];
  
  conditions.forEach(condition => {
    const invalidSymptoms = condition.symptoms.filter(symptomId => !symptomIds.has(symptomId));
    if (invalidSymptoms.length > 0) {
      invalidReferences.push({
        conditionId: condition.id,
        invalidSymptoms
      });
    }
  });
  
  return invalidReferences;
};

/**
 * Get emergency conditions (for medical safety)
 */
export const getEmergencyConditions = () => {
  return conditions.filter(condition => condition.triage_level === 'emergency');
};

/**
 * Get conditions by symptom
 */
export const getConditionsBySymptom = (symptomId: string) => {
  return conditions.filter(condition => condition.symptoms.includes(symptomId));
};

/**
 * Generate import summary
 */
export const generateImportSummary = () => {
  const stats = getImportStats();
  const validation = validateSymptomReferences();
  const emergencyConditions = getEmergencyConditions();
  
  return {
    ...stats,
    validationErrors: validation,
    emergencyConditionsCount: emergencyConditions.length,
    emergencyConditions: emergencyConditions.map(c => ({ id: c.id, name: c.name })),
    isValid: validation.length === 0
  };
};
