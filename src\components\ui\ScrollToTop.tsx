import { useState, useEffect } from 'react';
import { Fab, Zoom, useScrollTrigger } from '@mui/material';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

const ScrollToTop = () => {
  const [showButton, setShowButton] = useState(false);
  
  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 300
  });

  useEffect(() => {
    setShowButton(trigger);
  }, [trigger]);

  return (
    <Zoom in={showButton}>
      <Fab
        color="primary"
        size="small"
        aria-label="scroll back to top"
        onClick={handleScrollToTop}
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: 1000
        }}
      >
        <KeyboardArrowUpIcon />
      </Fab>
    </Zoom>
  );
};

export default ScrollToTop;