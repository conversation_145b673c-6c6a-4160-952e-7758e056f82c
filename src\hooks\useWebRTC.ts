import { useState, useEffect, useCallback, useRef } from 'react';
import { callService } from '../services/callService';
import { useFirebase } from '../contexts/FirebaseContext';
import type { CallSession } from '../types/chat';

export const useWebRTC = () => {
  const { currentUser } = useFirebase();
  const [currentCall, setCurrentCall] = useState<CallSession | null>(null);
  const [incomingCalls, setIncomingCalls] = useState<CallSession[]>([]);
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [callStatus, setCallStatus] = useState<string>('');

  // Refs
  const peerConnection = useRef<RTCPeerConnection | null>(null);
  const callUnsubscribe = useRef<(() => void) | null>(null);
  const incomingCallsUnsubscribe = useRef<(() => void) | null>(null);

  // Subscribe to incoming calls
  useEffect(() => {
    if (!currentUser?.uid) return;

    incomingCallsUnsubscribe.current = callService.subscribeToIncomingCalls(
      currentUser.uid,
      setIncomingCalls
    );

    return () => {
      if (incomingCallsUnsubscribe.current) {
        incomingCallsUnsubscribe.current();
      }
    };
  }, [currentUser?.uid]);

  // Setup peer connection
  const setupPeerConnection = useCallback((callId: string) => {
    if (peerConnection.current) {
      peerConnection.current.close();
    }

    peerConnection.current = callService.createPeerConnection(callId);
    const pc = peerConnection.current;

    // Handle remote stream
    pc.ontrack = (event) => {
      console.log('Received remote stream');
      setRemoteStream(event.streams[0]);
    };

    // Handle ICE candidates
    pc.onicecandidate = async (event) => {
      if (event.candidate && currentUser?.uid) {
        try {
          await callService.addIceCandidate(callId, currentUser.uid, event.candidate.toJSON());
        } catch (error) {
          console.error('Error adding ICE candidate:', error);
        }
      }
    };

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log('Connection state:', pc.connectionState);
      setCallStatus(pc.connectionState);
      
      if (pc.connectionState === 'connected') {
        setIsCallActive(true);
      } else if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {
        endCall();
      }
    };

    return pc;
  }, [currentUser?.uid]);

  // Initiate call
  const initiateCall = useCallback(async (
    conversationId: string, 
    calleeId: string, 
    type: 'video' | 'voice'
  ): Promise<string> => {
    if (!currentUser?.uid) {
      throw new Error('User not authenticated');
    }

    try {
      // Create call session
      const callId = await callService.initiateCall(conversationId, currentUser.uid, calleeId, type);
      
      // Get user media
      const stream = await callService.getUserMedia(type === 'video', true);
      setLocalStream(stream);
      setIsVideoEnabled(type === 'video');

      // Setup peer connection
      const pc = setupPeerConnection(callId);
      
      // Add local stream to peer connection
      stream.getTracks().forEach(track => {
        pc.addTrack(track, stream);
      });

      // Create and set offer
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      await callService.setOffer(callId, offer);

      // Subscribe to call updates
      callUnsubscribe.current = callService.subscribeToCall(callId, (call) => {
        setCurrentCall(call);
        handleCallUpdate(call, pc);
      });

      return callId;
    } catch (error) {
      console.error('Error initiating call:', error);
      throw error;
    }
  }, [currentUser?.uid, setupPeerConnection]);

  // Accept call
  const acceptCall = useCallback(async (call: CallSession) => {
    if (!currentUser?.uid) return;

    try {
      // Accept the call
      await callService.acceptCall(call.id);
      
      // Get user media
      const stream = await callService.getUserMedia(call.type === 'video', true);
      setLocalStream(stream);
      setIsVideoEnabled(call.type === 'video');

      // Setup peer connection
      const pc = setupPeerConnection(call.id);
      
      // Add local stream
      stream.getTracks().forEach(track => {
        pc.addTrack(track, stream);
      });

      // Set remote description (offer)
      if (call.offer) {
        await pc.setRemoteDescription(call.offer);
        
        // Create and set answer
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);
        await callService.setAnswer(call.id, answer);
      }

      // Subscribe to call updates
      callUnsubscribe.current = callService.subscribeToCall(call.id, (updatedCall) => {
        setCurrentCall(updatedCall);
        handleCallUpdate(updatedCall, pc);
      });

      setCurrentCall(call);
    } catch (error) {
      console.error('Error accepting call:', error);
      throw error;
    }
  }, [currentUser?.uid, setupPeerConnection]);

  // Handle call updates
  const handleCallUpdate = useCallback(async (call: CallSession, pc: RTCPeerConnection) => {
    if (!currentUser?.uid) return;

    try {
      // Handle answer
      if (call.answer && pc.remoteDescription === null) {
        await pc.setRemoteDescription(call.answer);
      }

      // Handle ICE candidates
      const otherUserId = call.callerId === currentUser.uid ? call.calleeId : call.callerId;
      const candidates = call.iceCandidates[otherUserId] || [];
      
      for (const candidate of candidates) {
        if (candidate) {
          await pc.addIceCandidate(new RTCIceCandidate(candidate));
        }
      }
    } catch (error) {
      console.error('Error handling call update:', error);
    }
  }, [currentUser?.uid]);

  // Decline call
  const declineCall = useCallback(async (callId: string) => {
    try {
      await callService.declineCall(callId);
      setIncomingCalls(prev => prev.filter(call => call.id !== callId));
    } catch (error) {
      console.error('Error declining call:', error);
    }
  }, []);

  // End call
  const endCall = useCallback(async () => {
    try {
      if (currentCall) {
        await callService.endCall(currentCall.id);
      }

      // Clean up streams
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        setLocalStream(null);
      }
      
      if (remoteStream) {
        remoteStream.getTracks().forEach(track => track.stop());
        setRemoteStream(null);
      }

      // Clean up peer connection
      if (peerConnection.current) {
        peerConnection.current.close();
        peerConnection.current = null;
      }

      // Clean up subscriptions
      if (callUnsubscribe.current) {
        callUnsubscribe.current();
        callUnsubscribe.current = null;
      }

      // Reset state
      setCurrentCall(null);
      setIsCallActive(false);
      setIsMuted(false);
      setIsVideoEnabled(true);
      setCallStatus('');
    } catch (error) {
      console.error('Error ending call:', error);
    }
  }, [currentCall, localStream, remoteStream]);

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsMuted(!audioTrack.enabled);
      }
    }
  }, [localStream]);

  // Toggle video
  const toggleVideo = useCallback(() => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
      }
    }
  }, [localStream]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
      if (remoteStream) {
        remoteStream.getTracks().forEach(track => track.stop());
      }
      if (peerConnection.current) {
        peerConnection.current.close();
      }
      if (callUnsubscribe.current) {
        callUnsubscribe.current();
      }
      if (incomingCallsUnsubscribe.current) {
        incomingCallsUnsubscribe.current();
      }
    };
  }, []);

  return {
    // State
    currentCall,
    incomingCalls,
    localStream,
    remoteStream,
    isCallActive,
    isMuted,
    isVideoEnabled,
    callStatus,

    // Actions
    initiateCall,
    acceptCall,
    declineCall,
    endCall,
    toggleMute,
    toggleVideo
  };
};
