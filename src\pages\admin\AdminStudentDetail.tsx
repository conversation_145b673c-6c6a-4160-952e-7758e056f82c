import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EmailIcon from '@mui/icons-material/Email';
import SchoolIcon from '@mui/icons-material/School';
import EventIcon from '@mui/icons-material/Event';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import HistoryIcon from '@mui/icons-material/History';
import NoteIcon from '@mui/icons-material/Note';
import { getUserById } from '../../services/userManagementService';
import { getAppointmentsByStudent } from '../../services/appointmentService';
import { getMedicalRecordsByStudent } from '../../services/medicalRecordService';
import type { UserProfile } from '../../types/firebase';

const AdminStudentDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [student, setStudent] = useState<UserProfile | null>(null);
  const [appointments, setAppointments] = useState<any[]>([]);
  const [medicalRecords, setMedicalRecords] = useState<any[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStudentData = async () => {
      if (!id) return;

      setLoading(true);
      try {
        // Fetch student profile
        const studentData = await getUserById(id);
        if (!studentData || studentData.role !== 'student') {
          setError('Student not found');
          return;
        }

        setStudent(studentData);

        // Fetch student's appointments and medical records
        try {
          const [appointmentsData, medicalRecordsData] = await Promise.all([
            getAppointmentsByStudent(id).catch(() => []),
            getMedicalRecordsByStudent(id).catch(() => [])
          ]);

          setAppointments(appointmentsData || []);
          setMedicalRecords(medicalRecordsData || []);
        } catch (dataError) {
          console.error('Error fetching student data:', dataError);
          // Don't set error for data, just use empty arrays
        }

      } catch (err) {
        console.error('Error fetching student:', err);
        setError('Failed to load student data');
      } finally {
        setLoading(false);
      }
    };

    fetchStudentData();
  }, [id]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Generate avatar text from name
  const generateAvatar = (name: string) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };

  // Format date helper
  const formatDate = (date: any): string => {
    if (!date) return 'N/A';

    try {
      if (date.toDate && typeof date.toDate === 'function') {
        return date.toDate().toLocaleDateString();
      }
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      return new Date(date).toLocaleDateString();
    } catch {
      return 'Invalid Date';
    }
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Container>
      </Layout>
    );
  }

  if (error || !student) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error || 'Student not found'}
          </Alert>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/admin/students')}
          >
            Back to Students
          </Button>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Student Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/students')}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Students
            </Button>
            <Button 
              variant="contained" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/admin/students/${id}/edit`)}
              sx={{ borderRadius: 2 }}
            >
              Edit Student
            </Button>
          </Box>
        </Box>
        
        {/* Student Profile */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar
                sx={{
                  width: 150,
                  height: 150,
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: 'secondary.main'
                }}
              >
                {generateAvatar(student.displayName || '')}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {student.displayName}
              </Typography>
              <Chip
                label={(student.status || 'active') === 'active' ? 'Active' : 'Inactive'}
                color={(student.status || 'active') === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Chip
                label={student.department || 'General Studies'}
                color="secondary"
                sx={{ mb: 3 }}
              />
            </Grid>
            
            {/* Contact and Details */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Student Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EmailIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email Address"
                    secondary={student.email}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <SchoolIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Department"
                    secondary={student.department || 'General Studies'}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Join Date"
                    secondary={formatDate(student.createdAt)}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
              
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 3 }}>
                Account Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <MedicalServicesIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Medical Records"
                    secondary={`${medicalRecords.length} records`}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <HistoryIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Appointments"
                    secondary={`${appointments.length} appointments`}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Updated"
                    secondary={formatDate(student.updatedAt)}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Tabs for Appointments and Medical Records */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange}
              aria-label="student details tabs"
              sx={{ px: 2 }}
            >
              <Tab label="Appointments" />
              <Tab label="Medical Records" />
            </Tabs>
          </Box>
          
          {/* Appointments Tab */}
          <Box role="tabpanel" hidden={tabValue !== 0} sx={{ p: 3 }}>
            {tabValue === 0 && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Appointment History
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="small"
                    onClick={() => navigate('/admin/appointments/new')}
                    sx={{ borderRadius: 2 }}
                  >
                    Schedule New Appointment
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                        <TableCell>Date</TableCell>
                        <TableCell>Time</TableCell>
                        <TableCell>Doctor</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {appointments.map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>{appointment.date}</TableCell>
                          <TableCell>{appointment.time}</TableCell>
                          <TableCell>{appointment.doctor}</TableCell>
                          <TableCell>{appointment.type}</TableCell>
                          <TableCell>
                            <Chip 
                              label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)} 
                              color={
                                appointment.status === 'completed' ? 'success' : 
                                appointment.status === 'scheduled' ? 'primary' : 
                                'default'
                              }
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Button 
                              size="small" 
                              onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {appointments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No appointments found for this student
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Box>
          
          {/* Medical Records Tab */}
          <Box role="tabpanel" hidden={tabValue !== 1} sx={{ p: 3 }}>
            {tabValue === 1 && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Medical Records
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="small"
                    onClick={() => navigate('/admin/medical-records/new')}
                    sx={{ borderRadius: 2 }}
                  >
                    Add Medical Record
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                        <TableCell>Date</TableCell>
                        <TableCell>Doctor</TableCell>
                        <TableCell>Diagnosis</TableCell>
                        <TableCell>Treatment</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {medicalRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{record.date}</TableCell>
                          <TableCell>{record.doctor}</TableCell>
                          <TableCell>{record.diagnosis}</TableCell>
                          <TableCell>{record.treatment}</TableCell>
                          <TableCell align="right">
                            <Button 
                              size="small" 
                              onClick={() => navigate(`/admin/medical-records/${record.id}`)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {medicalRecords.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No medical records found for this student
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminStudentDetail;