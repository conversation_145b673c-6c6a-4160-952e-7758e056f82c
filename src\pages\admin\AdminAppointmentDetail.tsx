import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EventIcon from '@mui/icons-material/Event';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PersonIcon from '@mui/icons-material/Person';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import CategoryIcon from '@mui/icons-material/Category';
import TimerIcon from '@mui/icons-material/Timer';
import NoteIcon from '@mui/icons-material/Note';
import CancelIcon from '@mui/icons-material/Cancel';

const AdminAppointmentDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [appointment, setAppointment] = useState(null);
  const [openCancelDialog, setOpenCancelDialog] = useState(false);
  
  useEffect(() => {
    // In a real app, you would fetch the appointment data from an API
    setLoading(true);
    setTimeout(() => {
      // Mock appointment data
      const mockAppointment = {
        id: parseInt(id),
        student: {
          id: 1,
          name: 'John Smith',
          email: '<EMAIL>',
          avatar: 'JS'
        },
        doctor: {
          id: 1,
          name: 'Dr. Sarah Johnson',
          specialty: 'Cardiology',
          avatar: 'SJ'
        },
        date: '2023-06-15',
        time: '14:30',
        endTime: '15:15',
        duration: 45,
        type: 'Follow-up',
        status: 'scheduled',
        notes: 'Follow-up appointment for previous treatment. Check blood pressure and heart rate.',
        createdAt: '2023-06-01'
      };
      
      setAppointment(mockAppointment);
      setLoading(false);
    }, 500);
  }, [id]);
  
  const handleOpenCancelDialog = () => {
    setOpenCancelDialog(true);
  };
  
  const handleCloseCancelDialog = () => {
    setOpenCancelDialog(false);
  };
  
  const handleCancelAppointment = () => {
    // In a real app, you would send a request to cancel the appointment
    console.log(`Cancelling appointment with ID: ${appointment.id}`);
    
    // Update the local state to reflect the cancellation
    setAppointment({
      ...appointment,
      status: 'cancelled'
    });
    
    handleCloseCancelDialog();
  };
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography>Loading appointment details...</Typography>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Appointment Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/appointments')}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Appointments
            </Button>
            {appointment.status === 'scheduled' && (
              <>
                <Button 
                  variant="outlined" 
                  color="error"
                  startIcon={<CancelIcon />}
                  onClick={handleOpenCancelDialog}
                  sx={{ borderRadius: 2, mr: 2 }}
                >
                  Cancel Appointment
                </Button>
                <Button 
                  variant="contained" 
                  startIcon={<EditIcon />}
                  onClick={() => navigate(`/admin/appointments/${id}/edit`)}
                  sx={{ borderRadius: 2 }}
                >
                  Edit Appointment
                </Button>
              </>
            )}
          </Box>
        </Box>
        
        {/* Appointment Status */}
        <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ mr: 2 }}>
            Status:
          </Typography>
          <Chip 
            label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)} 
            color={
              appointment.status === 'completed' ? 'success' : 
              appointment.status === 'scheduled' ? 'primary' : 
              'error'
            }
            sx={{ fontWeight: 'medium', fontSize: '0.9rem', height: 32 }}
          />
        </Box>
        
        {/* Appointment Details */}
        <Grid container spacing={4}>
          {/* Left Column - Appointment Info */}
          <Grid item xs={12} md={7}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                height: '100%'
              }}
            >
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Appointment Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Date" 
                    secondary={appointment.date}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <AccessTimeIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Time" 
                    secondary={`${appointment.time} - ${appointment.endTime}`}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <TimerIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Duration" 
                    secondary={`${appointment.duration} minutes`}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <CategoryIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Appointment Type" 
                    secondary={appointment.type}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                  />
                </ListItem>
                
                <ListItem disableGutters alignItems="flex-start">
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <NoteIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Notes" 
                    secondary={appointment.notes || 'No notes provided'}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>
          
          {/* Right Column - People Involved */}
          <Grid item xs={12} md={5}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                mb: 4
              }}
            >
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Student
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  sx={{ 
                    bgcolor: 'secondary.main',
                    width: 50,
                    height: 50,
                    mr: 2
                  }}
                >
                  {generateAvatar(appointment.student.name)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {appointment.student.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {appointment.student.email}
                  </Typography>
                </Box>
              </Box>
              
              <Button 
                variant="outlined" 
                size="small"
                onClick={() => navigate(`/admin/students/${appointment.student.id}`)}
                sx={{ borderRadius: 2 }}
              >
                View Student Profile
              </Button>
            </Paper>
            
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Doctor
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  sx={{ 
                    bgcolor: 'primary.main',
                    width: 50,
                    height: 50,
                    mr: 2
                  }}
                >
                  {generateAvatar(appointment.doctor.name)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {appointment.doctor.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {appointment.doctor.specialty}
                  </Typography>
                </Box>
              </Box>
              
              <Button 
                variant="outlined" 
                size="small"
                onClick={() => navigate(`/admin/doctors/${appointment.doctor.id}`)}
                sx={{ borderRadius: 2 }}
              >
                View Doctor Profile
              </Button>
            </Paper>
          </Grid>
        </Grid>
        
        {/* Appointment History */}
        <Paper 
          sx={{ 
            p: 3, 
            mt: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Typography variant="h6" fontWeight="medium" gutterBottom>
            Appointment History
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <List disablePadding>
            <ListItem disableGutters>
              <ListItemText 
                primary={`Appointment created on ${appointment.createdAt}`}
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
            {appointment.status === 'cancelled' && (
              <ListItem disableGutters>
                <ListItemText 
                  primary="Appointment cancelled"
                  primaryTypographyProps={{ variant: 'body2', color: 'error.main' }}
                />
              </ListItem>
            )}
            {appointment.status === 'completed' && (
              <ListItem disableGutters>
                <ListItemText 
                  primary="Appointment completed"
                  primaryTypographyProps={{ variant: 'body2', color: 'success.main' }}
                />
              </ListItem>
            )}
          </List>
        </Paper>
      </Container>
      
      {/* Cancel Appointment Dialog */}
      <Dialog
        open={openCancelDialog}
        onClose={handleCloseCancelDialog}
        aria-labelledby="cancel-dialog-title"
        aria-describedby="cancel-dialog-description"
      >
        <DialogTitle id="cancel-dialog-title">
          {"Confirm Appointment Cancellation"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="cancel-dialog-description">
            Are you sure you want to cancel this appointment? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button 
            onClick={handleCloseCancelDialog} 
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            No, Keep It
          </Button>
          <Button 
            onClick={handleCancelAppointment} 
            color="error" 
            variant="contained"
            sx={{ borderRadius: 2 }}
            autoFocus
          >
            Yes, Cancel It
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default AdminAppointmentDetail;