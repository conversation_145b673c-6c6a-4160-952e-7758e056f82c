// Student ID Generation Service
// Generates unique student IDs in format STU-YYYY-XXXX

import { collection, query, where, getDocs, doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from './firebase';

export interface StudentIdCounter {
  year: number;
  lastNumber: number;
  updatedAt: Date;
}

/**
 * Generate next available student ID
 * Format: STU-YYYY-XXXX where YYYY is current year and XXXX is sequential number
 */
export const generateStudentId = async (): Promise<string> => {
  try {
    const currentYear = new Date().getFullYear();
    const counterDocId = `studentIdCounter_${currentYear}`;
    const counterRef = doc(db, 'studentIdCounters', counterDocId);
    
    // Get current counter for this year
    const counterDoc = await getDoc(counterRef);
    
    let nextNumber = 1;
    
    if (counterDoc.exists()) {
      const counterData = counterDoc.data() as StudentIdCounter;
      nextNumber = counterData.lastNumber + 1;
    }
    
    // Update counter
    await setDoc(counterRef, {
      year: currentYear,
      lastNumber: nextNumber,
      updatedAt: new Date()
    });
    
    // Format: STU-2024-0001
    const studentId = `STU-${currentYear}-${nextNumber.toString().padStart(4, '0')}`;
    
    console.log(`✅ Generated student ID: ${studentId}`);
    return studentId;
    
  } catch (error) {
    console.error('❌ Error generating student ID:', error);
    // Fallback to timestamp-based ID if Firebase fails
    const timestamp = Date.now();
    const fallbackId = `STU-${new Date().getFullYear()}-${timestamp.toString().slice(-4)}`;
    console.log(`⚠️ Using fallback student ID: ${fallbackId}`);
    return fallbackId;
  }
};

/**
 * Check if a student ID already exists
 */
export const isStudentIdUnique = async (studentId: string): Promise<boolean> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('studentId', '==', studentId)
    );
    
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.empty;
    
  } catch (error) {
    console.error('❌ Error checking student ID uniqueness:', error);
    return false;
  }
};

/**
 * Get current year's student count
 */
export const getCurrentYearStudentCount = async (): Promise<number> => {
  try {
    const currentYear = new Date().getFullYear();
    const counterDocId = `studentIdCounter_${currentYear}`;
    const counterRef = doc(db, 'studentIdCounters', counterDocId);
    
    const counterDoc = await getDoc(counterRef);
    
    if (counterDoc.exists()) {
      const counterData = counterDoc.data() as StudentIdCounter;
      return counterData.lastNumber;
    }
    
    return 0;
    
  } catch (error) {
    console.error('❌ Error getting student count:', error);
    return 0;
  }
};

/**
 * Validate student ID format
 */
export const validateStudentIdFormat = (studentId: string): boolean => {
  const pattern = /^STU-\d{4}-\d{4}$/;
  return pattern.test(studentId);
};

/**
 * Extract year from student ID
 */
export const extractYearFromStudentId = (studentId: string): number | null => {
  const match = studentId.match(/^STU-(\d{4})-\d{4}$/);
  return match ? parseInt(match[1]) : null;
};

/**
 * Extract sequence number from student ID
 */
export const extractSequenceFromStudentId = (studentId: string): number | null => {
  const match = studentId.match(/^STU-\d{4}-(\d{4})$/);
  return match ? parseInt(match[1]) : null;
};

/**
 * Get all student IDs for a specific year
 */
export const getStudentIdsByYear = async (year: number): Promise<string[]> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('role', '==', 'student')
    );
    
    const querySnapshot = await getDocs(usersQuery);
    const studentIds: string[] = [];
    
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      if (userData.studentId && extractYearFromStudentId(userData.studentId) === year) {
        studentIds.push(userData.studentId);
      }
    });
    
    return studentIds.sort();
    
  } catch (error) {
    console.error('❌ Error getting student IDs by year:', error);
    return [];
  }
};

/**
 * Initialize student ID counter for a new year
 */
export const initializeYearCounter = async (year: number): Promise<void> => {
  try {
    const counterDocId = `studentIdCounter_${year}`;
    const counterRef = doc(db, 'studentIdCounters', counterDocId);
    
    const counterDoc = await getDoc(counterRef);
    
    if (!counterDoc.exists()) {
      await setDoc(counterRef, {
        year,
        lastNumber: 0,
        updatedAt: new Date()
      });
      
      console.log(`✅ Initialized student ID counter for year ${year}`);
    }
    
  } catch (error) {
    console.error(`❌ Error initializing counter for year ${year}:`, error);
  }
};
