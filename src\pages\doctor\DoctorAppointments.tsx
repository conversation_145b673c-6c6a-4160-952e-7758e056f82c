import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Grid, Paper, Button,
  List, ListItem, ListItemText, Avatar, Divider,
  TextField, InputAdornment, Chip, Card, CardContent,
  IconButton, Badge, Dialog, DialogTitle, DialogContent,
  DialogActions, Tab, Tabs, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Menu, MenuItem
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import SearchIcon from '@mui/icons-material/Search';
import ChatIcon from '@mui/icons-material/Chat';
import VideoCallIcon from '@mui/icons-material/VideoCall';
import PhoneIcon from '@mui/icons-material/Phone';
import EventIcon from '@mui/icons-material/Event';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ScheduleIcon from '@mui/icons-material/Schedule';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useFirebase } from '../../contexts/FirebaseContext';

const DoctorAppointments = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedAppointment, setSelectedAppointment] = useState(null);

  // Mock appointment data
  const [appointments, setAppointments] = useState([
    {
      id: 1,
      patient: 'John Smith',
      studentId: 'ST001',
      date: '2024-01-20',
      time: '09:00 AM',
      duration: '30 min',
      type: 'Consultation',
      status: 'upcoming',
      reason: 'Routine check-up',
      notes: 'Patient reports feeling well overall',
      avatar: ''
    },
    {
      id: 2,
      patient: 'Emma Davis',
      studentId: 'ST002',
      date: '2024-01-20',
      time: '10:30 AM',
      duration: '45 min',
      type: 'Follow-up',
      status: 'upcoming',
      reason: 'Anxiety management review',
      notes: 'Follow-up on medication effectiveness',
      avatar: ''
    },
    {
      id: 3,
      patient: 'Alex Johnson',
      studentId: 'ST003',
      date: '2024-01-19',
      time: '02:00 PM',
      duration: '30 min',
      type: 'Check-up',
      status: 'completed',
      reason: 'Migraine treatment review',
      notes: 'Patient responded well to new medication',
      avatar: ''
    },
    {
      id: 4,
      patient: 'Sarah Wilson',
      studentId: 'ST004',
      date: '2024-01-18',
      time: '03:30 PM',
      duration: '30 min',
      type: 'Consultation',
      status: 'cancelled',
      reason: 'General health concerns',
      notes: 'Patient cancelled due to scheduling conflict',
      avatar: ''
    },
    {
      id: 5,
      patient: 'Michael Brown',
      studentId: 'ST005',
      date: '2024-01-21',
      time: '11:00 AM',
      duration: '30 min',
      type: 'Consultation',
      status: 'upcoming',
      reason: 'Headache evaluation',
      notes: 'New patient consultation',
      avatar: ''
    }
  ]);

  const getFilteredAppointments = () => {
    let filtered = appointments;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(appointment =>
        appointment.patient.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.reason.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by tab
    switch (selectedTab) {
      case 0: // All
        return filtered;
      case 1: // Today
        const today = new Date().toISOString().split('T')[0];
        return filtered.filter(apt => apt.date === today);
      case 2: // Upcoming
        return filtered.filter(apt => apt.status === 'upcoming');
      case 3: // Completed
        return filtered.filter(apt => apt.status === 'completed');
      default:
        return filtered;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'upcoming':
        return 'primary';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'upcoming':
        return <ScheduleIcon />;
      case 'completed':
        return <CheckCircleIcon />;
      case 'cancelled':
        return <CancelIcon />;
      default:
        return <ScheduleIcon />;
    }
  };

  const handleMenuClick = (event, appointment) => {
    setAnchorEl(event.currentTarget);
    setSelectedAppointment(appointment);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedAppointment(null);
  };

  const handleStartChat = (appointment) => {
    navigate('/doctor/chat', { state: { selectedPatient: { name: appointment.patient } } });
    handleMenuClose();
  };

  const handleVideoCall = (appointment) => {
    alert(`Starting video call with ${appointment.patient}`);
    handleMenuClose();
  };

  const handleVoiceCall = (appointment) => {
    alert(`Starting voice call with ${appointment.patient}`);
    handleMenuClose();
  };

  const handleCompleteAppointment = (appointmentId) => {
    setAppointments(prev => 
      prev.map(apt => 
        apt.id === appointmentId 
          ? { ...apt, status: 'completed' }
          : apt
      )
    );
    handleMenuClose();
  };

  const handleCancelAppointment = (appointmentId) => {
    setAppointments(prev => 
      prev.map(apt => 
        apt.id === appointmentId 
          ? { ...apt, status: 'cancelled' }
          : apt
      )
    );
    handleMenuClose();
  };

  const filteredAppointments = getFilteredAppointments();

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Appointments
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your patient appointments and schedule
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<EventIcon />}
            sx={{ borderRadius: 2 }}
          >
            New Appointment
          </Button>
        </Box>

        {/* Search and Filters */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                placeholder="Search appointments by patient name, ID, or reason..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterListIcon />}
                sx={{ borderRadius: 2, height: 56 }}
              >
                Advanced Filters
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)}>
            <Tab label={`All (${appointments.length})`} />
            <Tab label={`Today (${appointments.filter(apt => apt.date === new Date().toISOString().split('T')[0]).length})`} />
            <Tab label={`Upcoming (${appointments.filter(apt => apt.status === 'upcoming').length})`} />
            <Tab label={`Completed (${appointments.filter(apt => apt.status === 'completed').length})`} />
          </Tabs>
        </Box>

        {/* Appointments Table */}
        <Paper sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Patient</TableCell>
                  <TableCell>Date & Time</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredAppointments.map((appointment) => (
                  <TableRow key={appointment.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: 'primary.light' }}>
                          {appointment.patient.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {appointment.patient}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {appointment.studentId}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {appointment.date}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {appointment.time} ({appointment.duration})
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {appointment.type}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {appointment.reason}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(appointment.status)}
                        label={appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                        color={getStatusColor(appointment.status)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <IconButton 
                          size="small" 
                          color="primary"
                          onClick={() => handleStartChat(appointment)}
                        >
                          <ChatIcon />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="success"
                          onClick={() => handleVideoCall(appointment)}
                        >
                          <VideoCallIcon />
                        </IconButton>
                        <IconButton 
                          size="small"
                          onClick={(e) => handleMenuClick(e, appointment)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {selectedAppointment?.status === 'upcoming' && (
            <MenuItem onClick={() => handleCompleteAppointment(selectedAppointment.id)}>
              <CheckCircleIcon sx={{ mr: 1 }} />
              Mark as Completed
            </MenuItem>
          )}
          {selectedAppointment?.status === 'upcoming' && (
            <MenuItem onClick={() => handleCancelAppointment(selectedAppointment.id)}>
              <CancelIcon sx={{ mr: 1 }} />
              Cancel Appointment
            </MenuItem>
          )}
          <MenuItem onClick={() => handleVoiceCall(selectedAppointment)}>
            <PhoneIcon sx={{ mr: 1 }} />
            Voice Call
          </MenuItem>
          <MenuItem onClick={() => handleStartChat(selectedAppointment)}>
            <ChatIcon sx={{ mr: 1 }} />
            Send Message
          </MenuItem>
        </Menu>
      </Container>
    </Layout>
  );
};

export default DoctorAppointments;
