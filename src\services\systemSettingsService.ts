import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';

export interface SystemSettings {
  // General Settings
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportPhone: string;
  
  // Appointment Settings
  appointmentDuration: number; // in minutes
  maxAdvanceBookingDays: number;
  allowCancellationHours: number;
  autoConfirmAppointments: boolean;
  
  // Notification Settings
  emailNotifications: boolean;
  smsNotifications: boolean;
  appointmentReminders: boolean;
  reminderHoursBefore: number;
  
  // Medical Settings
  requireFollowUpApproval: boolean;
  maxMedicationsPerRecord: number;
  allowStudentViewRecords: boolean;
  recordRetentionYears: number;
  
  // Security Settings
  sessionTimeoutMinutes: number;
  requireTwoFactor: boolean;
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  
  // Communication Settings
  allowVideoCall: boolean;
  allowVoiceCall: boolean;
  allowFileSharing: boolean;
  maxFileSize: number; // in MB
  
  // System Maintenance
  maintenanceMode: boolean;
  maintenanceMessage: string;
  backupFrequencyDays: number;
  
  // Metadata
  lastUpdated: Date;
  updatedBy: string;
}

const DEFAULT_SETTINGS: SystemSettings = {
  // General Settings
  siteName: 'University Health Portal',
  siteDescription: 'Comprehensive healthcare management system for students',
  contactEmail: '<EMAIL>',
  supportPhone: '+****************',
  
  // Appointment Settings
  appointmentDuration: 30,
  maxAdvanceBookingDays: 30,
  allowCancellationHours: 24,
  autoConfirmAppointments: true,
  
  // Notification Settings
  emailNotifications: true,
  smsNotifications: false,
  appointmentReminders: true,
  reminderHoursBefore: 24,
  
  // Medical Settings
  requireFollowUpApproval: false,
  maxMedicationsPerRecord: 10,
  allowStudentViewRecords: true,
  recordRetentionYears: 7,
  
  // Security Settings
  sessionTimeoutMinutes: 60,
  requireTwoFactor: false,
  passwordMinLength: 8,
  passwordRequireSpecialChars: true,
  
  // Communication Settings
  allowVideoCall: true,
  allowVoiceCall: true,
  allowFileSharing: true,
  maxFileSize: 10,
  
  // System Maintenance
  maintenanceMode: false,
  maintenanceMessage: 'System is currently under maintenance. Please try again later.',
  backupFrequencyDays: 1,
  
  // Metadata
  lastUpdated: new Date(),
  updatedBy: 'system'
};

/**
 * Get system settings
 */
export const getSystemSettings = async (): Promise<SystemSettings> => {
  try {
    const docRef = doc(db, 'systemSettings', 'main');
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        ...data,
        lastUpdated: data.lastUpdated?.toDate() || new Date()
      } as SystemSettings;
    } else {
      // Create default settings if they don't exist
      await setDoc(docRef, {
        ...DEFAULT_SETTINGS,
        lastUpdated: Timestamp.now()
      });
      console.log('✅ Default system settings created');
      return DEFAULT_SETTINGS;
    }
  } catch (error) {
    console.error('❌ Error fetching system settings:', error);
    return DEFAULT_SETTINGS;
  }
};

/**
 * Update system settings
 */
export const updateSystemSettings = async (
  settings: Partial<SystemSettings>, 
  updatedBy: string
): Promise<void> => {
  try {
    const docRef = doc(db, 'systemSettings', 'main');
    await updateDoc(docRef, {
      ...settings,
      lastUpdated: Timestamp.now(),
      updatedBy
    });
    console.log('✅ System settings updated');
  } catch (error) {
    console.error('❌ Error updating system settings:', error);
    throw error;
  }
};

/**
 * Reset settings to default
 */
export const resetSystemSettings = async (updatedBy: string): Promise<void> => {
  try {
    const docRef = doc(db, 'systemSettings', 'main');
    await setDoc(docRef, {
      ...DEFAULT_SETTINGS,
      lastUpdated: Timestamp.now(),
      updatedBy
    });
    console.log('✅ System settings reset to default');
  } catch (error) {
    console.error('❌ Error resetting system settings:', error);
    throw error;
  }
};

/**
 * Check if system is in maintenance mode
 */
export const isMaintenanceMode = async (): Promise<boolean> => {
  try {
    const settings = await getSystemSettings();
    return settings.maintenanceMode;
  } catch (error) {
    console.error('❌ Error checking maintenance mode:', error);
    return false;
  }
};

/**
 * Toggle maintenance mode
 */
export const toggleMaintenanceMode = async (
  enabled: boolean, 
  message: string, 
  updatedBy: string
): Promise<void> => {
  try {
    await updateSystemSettings({
      maintenanceMode: enabled,
      maintenanceMessage: message
    }, updatedBy);
    console.log(`✅ Maintenance mode ${enabled ? 'enabled' : 'disabled'}`);
  } catch (error) {
    console.error('❌ Error toggling maintenance mode:', error);
    throw error;
  }
};

/**
 * Get settings categories for UI organization
 */
export const getSettingsCategories = () => {
  return [
    {
      id: 'general',
      name: 'General Settings',
      description: 'Basic site configuration',
      fields: ['siteName', 'siteDescription', 'contactEmail', 'supportPhone']
    },
    {
      id: 'appointments',
      name: 'Appointment Settings',
      description: 'Configure appointment booking and management',
      fields: ['appointmentDuration', 'maxAdvanceBookingDays', 'allowCancellationHours', 'autoConfirmAppointments']
    },
    {
      id: 'notifications',
      name: 'Notification Settings',
      description: 'Manage notification preferences',
      fields: ['emailNotifications', 'smsNotifications', 'appointmentReminders', 'reminderHoursBefore']
    },
    {
      id: 'medical',
      name: 'Medical Settings',
      description: 'Medical record and treatment settings',
      fields: ['requireFollowUpApproval', 'maxMedicationsPerRecord', 'allowStudentViewRecords', 'recordRetentionYears']
    },
    {
      id: 'security',
      name: 'Security Settings',
      description: 'Security and authentication settings',
      fields: ['sessionTimeoutMinutes', 'requireTwoFactor', 'passwordMinLength', 'passwordRequireSpecialChars']
    },
    {
      id: 'communication',
      name: 'Communication Settings',
      description: 'Chat and call configuration',
      fields: ['allowVideoCall', 'allowVoiceCall', 'allowFileSharing', 'maxFileSize']
    },
    {
      id: 'maintenance',
      name: 'System Maintenance',
      description: 'System maintenance and backup settings',
      fields: ['maintenanceMode', 'maintenanceMessage', 'backupFrequencyDays']
    }
  ];
};
