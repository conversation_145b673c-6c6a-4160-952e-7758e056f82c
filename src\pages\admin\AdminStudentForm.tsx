import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Snackbar,
  Alert,
  Avatar
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';

const AdminStudentForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = Boolean(id);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    department: '',
    status: 'active',
    joinDate: '',
    avatar: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  
  // Mock departments data
  const departments = ['Computer Science', 'Psychology', 'Business', 'Medicine', 'Engineering', 'Arts'];
  
  useEffect(() => {
    if (isEditMode) {
      // In a real app, you would fetch the student data from an API
      // For now, we'll use mock data
      setLoading(true);
      setTimeout(() => {
        // Mock student data
        const mockStudent = {
          id: parseInt(id),
          name: 'John Smith',
          email: '<EMAIL>',
          department: 'Computer Science',
          status: 'active',
          joinDate: '2023-01-15',
          avatar: 'JS'
        };
        
        setFormData({
          name: mockStudent.name,
          email: mockStudent.email,
          department: mockStudent.department,
          status: mockStudent.status,
          joinDate: mockStudent.joinDate,
          avatar: mockStudent.avatar
        });
        
        setLoading(false);
      }, 500);
    }
  }, [id, isEditMode]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    
    // In a real app, you would send the data to an API
    setTimeout(() => {
      setLoading(false);
      setSnackbarMessage(isEditMode ? 'Student updated successfully!' : 'Student created successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Navigate back to students list after a short delay
      setTimeout(() => {
        navigate('/admin/students');
      }, 1500);
    }, 1000);
  };
  
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            {isEditMode ? 'Edit Student' : 'Add New Student'}
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/admin/students')}
            sx={{ borderRadius: 2 }}
          >
            Back to Students
          </Button>
        </Box>
        
        {/* Form */}
        <Paper 
          component="form"
          onSubmit={handleSubmit}
          sx={{ 
            p: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={3}>
            {/* Avatar Preview */}
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <Avatar 
                sx={{ 
                  width: 100, 
                  height: 100, 
                  fontSize: '2rem',
                  bgcolor: 'secondary.main'
                }}
              >
                {generateAvatar(formData.name)}
              </Avatar>
            </Grid>
            
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Email Address"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Department</InputLabel>
                <Select
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  label="Department"
                  disabled={loading}
                >
                  {departments.map((dept) => (
                    <MenuItem key={dept} value={dept}>{dept}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Status"
                  disabled={loading}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Join Date"
                name="joinDate"
                type="date"
                value={formData.joinDate}
                onChange={handleChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            
            {/* Submit Button */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                startIcon={<SaveIcon />}
                disabled={loading}
                sx={{ 
                  borderRadius: 2,
                  px: 4,
                  py: 1.5
                }}
              >
                {loading ? 'Saving...' : isEditMode ? 'Update Student' : 'Create Student'}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </Container>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity} 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminStudentForm;