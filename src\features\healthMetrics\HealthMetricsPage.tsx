import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Grid, Button, 
  TextField, MenuItem, Tabs, Tab, Card, CardContent,
  CircularProgress, Divider
} from '@mui/material';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, 
  ResponsiveContainer, BarChart, Bar, Legend
} from 'recharts';
import { 
  MonitorHeart as HeartIcon,
  Scale as WeightIcon,
  LocalHospital as BloodIcon,
  DirectionsRun as ActivityIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';

const HealthMetricsPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMetric, setNewMetric] = useState({
    type: 'weight',
    value: '',
    systolic: '',
    diastolic: '',
    steps: '',
    calories: '',
    date: new Date().toISOString().split('T')[0]
  });
  const [metrics, setMetrics] = useState({
    weight: [],
    bloodPressure: [],
    heartRate: [],
    activity: []
  });
  const { currentUser } = useFirebase();

  useEffect(() => {
    // Mock data
    setTimeout(() => {
      const today = new Date();
      const mockData = {
        weight: Array.from({ length: 10 }, (_, i) => ({
          date: new Date(today.getTime() - (9 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          value: Math.round((70 + Math.random() * 2) * 10) / 10
        })),
        bloodPressure: Array.from({ length: 10 }, (_, i) => ({
          date: new Date(today.getTime() - (9 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          systolic: Math.round(120 + Math.random() * 10),
          diastolic: Math.round(80 + Math.random() * 5)
        })),
        heartRate: Array.from({ length: 10 }, (_, i) => ({
          date: new Date(today.getTime() - (9 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          value: Math.round(70 + Math.random() * 10)
        })),
        activity: Array.from({ length: 10 }, (_, i) => ({
          date: new Date(today.getTime() - (9 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          steps: Math.round(5000 + Math.random() * 3000),
          calories: Math.round(300 + Math.random() * 200)
        }))
      };
      
      setMetrics(mockData);
      setLoading(false);
    }, 1000);
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleAddMetric = () => {
    setShowAddForm(true);
    // Set default type based on active tab
    const types = ['weight', 'bloodPressure', 'heartRate', 'activity'];
    setNewMetric({
      ...newMetric,
      type: types[activeTab]
    });
  };

  const handleCancelAdd = () => {
    setShowAddForm(false);
    setNewMetric({
      type: 'weight',
      value: '',
      systolic: '',
      diastolic: '',
      steps: '',
      calories: '',
      date: new Date().toISOString().split('T')[0]
    });
  };

  const handleMetricTypeChange = (event) => {
    setNewMetric({ ...newMetric, type: event.target.value });
  };

  const handleMetricValueChange = (event) => {
    setNewMetric({ ...newMetric, value: event.target.value });
  };

  const handleMetricDateChange = (event) => {
    setNewMetric({ ...newMetric, date: event.target.value });
  };

  const handleSaveMetric = () => {
    setLoading(true);
    
    // Add the new metric to the appropriate array
    setTimeout(() => {
      if (newMetric.type === 'weight') {
        const updatedWeights = [...metrics.weight, { 
          date: newMetric.date, 
          value: parseFloat(newMetric.value) 
        }];
        updatedWeights.sort((a, b) => new Date(a.date) - new Date(b.date));
        setMetrics({ ...metrics, weight: updatedWeights });
      } else if (newMetric.type === 'heartRate') {
        const updatedHeartRates = [...metrics.heartRate, { 
          date: newMetric.date, 
          value: parseInt(newMetric.value) 
        }];
        updatedHeartRates.sort((a, b) => new Date(a.date) - new Date(b.date));
        setMetrics({ ...metrics, heartRate: updatedHeartRates });
      }
      
      // Reset form
      handleCancelAdd();
      setLoading(false);
    }, 1000);
  };

  const renderWeightTab = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Weight History
      </Typography>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={metrics.weight}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis domain={['dataMin - 1', 'dataMax + 1']} />
          <Tooltip />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="#8884d8" 
            activeDot={{ r: 8 }} 
            name="Weight (kg)"
          />
        </LineChart>
      </ResponsiveContainer>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Recent Measurements
        </Typography>
        <Grid container spacing={2}>
          {metrics.weight.slice(-3).reverse().map((item, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h5" component="div">
                    {item.value} kg
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(item.date).toLocaleDateString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );

  const renderBloodPressureTab = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Blood Pressure History
      </Typography>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={metrics.bloodPressure}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis domain={[60, 160]} />
          <Tooltip />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="systolic" 
            stroke="#ff0000" 
            activeDot={{ r: 8 }} 
            name="Systolic (mmHg)"
          />
          <Line 
            type="monotone" 
            dataKey="diastolic" 
            stroke="#0000ff" 
            activeDot={{ r: 8 }} 
            name="Diastolic (mmHg)"
          />
        </LineChart>
      </ResponsiveContainer>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Recent Measurements
        </Typography>
        <Grid container spacing={2}>
          {metrics.bloodPressure.slice(-3).reverse().map((item, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h5" component="div">
                    {item.systolic}/{item.diastolic}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(item.date).toLocaleDateString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );

  const renderHeartRateTab = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Heart Rate History
      </Typography>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={metrics.heartRate}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis domain={[50, 100]} />
          <Tooltip />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="#ff4081" 
            activeDot={{ r: 8 }} 
            name="Heart Rate (bpm)"
          />
        </LineChart>
      </ResponsiveContainer>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Recent Measurements
        </Typography>
        <Grid container spacing={2}>
          {metrics.heartRate.slice(-3).reverse().map((item, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h5" component="div">
                    {item.value} bpm
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(item.date).toLocaleDateString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );

  const renderActivityTab = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Activity History
      </Typography>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={metrics.activity}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
          <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
          <Tooltip />
          <Legend />
          <Bar yAxisId="left" dataKey="steps" fill="#8884d8" name="Steps" />
          <Bar yAxisId="right" dataKey="calories" fill="#82ca9d" name="Calories" />
        </BarChart>
      </ResponsiveContainer>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Recent Activity
        </Typography>
        <Grid container spacing={2}>
          {metrics.activity.slice(-3).reverse().map((item, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" component="div">
                    {item.steps} steps
                  </Typography>
                  <Typography variant="body1">
                    {item.calories} calories
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(item.date).toLocaleDateString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );

  const renderAddForm = () => (
    <Box sx={{ mt: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
      <Typography variant="h6" gutterBottom>
        Add New Measurement
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <TextField
            select
            label="Metric Type"
            value={newMetric.type}
            onChange={handleMetricTypeChange}
            fullWidth
          >
            <MenuItem value="weight">Weight</MenuItem>
            <MenuItem value="heartRate">Heart Rate</MenuItem>
          </TextField>
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Value"
            type="number"
            value={newMetric.value}
            onChange={handleMetricValueChange}
            fullWidth
            InputProps={{
              endAdornment: newMetric.type === 'weight' ? 'kg' : 'bpm'
            }}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Date"
            type="date"
            value={newMetric.date}
            onChange={handleMetricDateChange}
            fullWidth
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button variant="outlined" onClick={handleCancelAdd}>
              Cancel
            </Button>
            <Button 
              variant="contained" 
              onClick={handleSaveMetric}
              disabled={!newMetric.value}
            >
              Save
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Health Metrics
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleAddMetric}
            disabled={showAddForm}
          >
            Add Measurement
          </Button>
        </Box>
        
        {showAddForm && renderAddForm()}
        
        <Paper
          elevation={0}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',  // Updated to match dashboard style
            mt: 3
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2,
                fontWeight: 'medium'
              }
            }}
          >
                <Tab icon={<WeightIcon />} label="Weight" />
                <Tab icon={<BloodIcon />} label="Blood Pressure" />
                <Tab icon={<HeartIcon />} label="Heart Rate" />
                <Tab icon={<ActivityIcon />} label="Activity" />
              </Tabs>

              <Box sx={{ p: 3 }}>
                {activeTab === 0 && renderWeightTab()}
                {activeTab === 1 && renderBloodPressureTab()}
                {activeTab === 2 && renderHeartRateTab()}
                {activeTab === 3 && renderActivityTab()}
              </Box>
            </>
          )}
        </Paper>
      </Container>
    </Layout>
  );
};

export default HealthMetricsPage;

