import { Box, Container, Typography, Paper, PaperProps } from '@mui/material';

interface PageBannerProps extends PaperProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  height?: string | number;
}

const PageBanner = ({ 
  title, 
  subtitle, 
  backgroundImage,
  height = 300,
  ...props 
}: PageBannerProps) => {
  return (
    <Paper
      elevation={0}
      sx={{
        position: 'relative',
        height,
        display: 'flex',
        alignItems: 'center',
        borderRadius: 0,
        backgroundImage: backgroundImage 
          ? `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url(${backgroundImage})`
          : 'linear-gradient(135deg, #0072ff 0%, #0044b3 100%)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        color: 'white',
        mb: 6,
        ...props.sx
      }}
      {...props}
    >
      <Container maxWidth="lg">
        <Box sx={{ maxWidth: 800 }}>
          <Typography 
            variant="h2" 
            component="h1" 
            gutterBottom
            sx={{ 
              fontWeight: 'bold',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}
          >
            {title}
          </Typography>
          {subtitle && (
            <Typography 
              variant="h5"
              sx={{ 
                opacity: 0.9,
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      </Container>
    </Paper>
  );
};

export default PageBanner;
