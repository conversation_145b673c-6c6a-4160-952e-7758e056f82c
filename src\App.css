/* Base styles */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-color: #333;
  --light-bg: #f5f5f5;
  --white: #ffffff;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background-color: var(--light-bg);
}

/* Layout */
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header */
.app-header {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-header nav ul {
  display: flex;
  list-style: none;
  gap: 1.5rem;
}

.app-header nav a {
  color: var(--white);
  text-decoration: none;
}

.auth-buttons button {
  background-color: var(--secondary-color);
  color: var(--white);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

/* Footer */
.app-footer {
  background-color: var(--primary-color);
  color: var(--white);
  text-align: center;
  padding: 1rem;
}

/* Feature cards */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background-color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

/* White button style */
.white-button {
  background-color: white !important;
  color: #0072ff !important;
  box-shadow: none !important;
}

.white-button:hover {
  background-color: white !important;
  box-shadow: none !important;
}

/* Add this to ensure the iframe inherits the border radius */
iframe {
  border-radius: inherit;
}

