import React, { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/layout/Layout';
import { getAppointmentsByStudent, type Appointment } from '../../services/appointmentService';
import MedicationIcon from '@mui/icons-material/Medication';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ChatIcon from '@mui/icons-material/Chat';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import ArticleIcon from '@mui/icons-material/Article';
import PersonIcon from '@mui/icons-material/Person';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';

const Dashboard = () => {
  const navigate = useNavigate();
  const { currentUser, userProfile, loading } = useAuth();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loadingAppointments, setLoadingAppointments] = useState(true);

  // Redirect users to their appropriate dashboard based on role
  useEffect(() => {
    if (!loading && userProfile) {
      console.log('📊 Dashboard: Checking user role for redirect:', {
        role: userProfile.role,
        uid: userProfile.uid,
        email: userProfile.email
      });

      switch (userProfile.role) {
        case 'admin':
          console.log('👑 Dashboard: Admin detected, redirecting to admin dashboard');
          navigate('/admin/dashboard', { replace: true });
          return;
        case 'doctor':
          console.log('👨‍⚕️ Dashboard: Doctor detected, redirecting to doctor dashboard');
          navigate('/doctor/dashboard', { replace: true });
          return;
        case 'student':
          console.log('🎓 Dashboard: Student detected, staying on student dashboard');
          // Students stay on this dashboard
          break;
        default:
          console.log('❓ Dashboard: Unknown role, staying on default dashboard');
          // Unknown role, stay on default dashboard
          break;
      }
    }
  }, [loading, userProfile, navigate]);

  // Load student's appointments
  useEffect(() => {
    const loadAppointments = async () => {
      if (!currentUser?.uid || !userProfile || userProfile.role !== 'student') {
        setLoadingAppointments(false);
        return;
      }

      try {
        setLoadingAppointments(true);
        const studentAppointments = await getAppointmentsByStudent(currentUser.uid);

        // Filter for upcoming appointments only
        const today = new Date();
        const upcomingAppointments = studentAppointments.filter(apt => {
          const appointmentDate = new Date(apt.date);
          return appointmentDate >= today && apt.status === 'scheduled';
        });

        setAppointments(upcomingAppointments);
        console.log(`📅 Loaded ${upcomingAppointments.length} upcoming appointments for student`);
      } catch (error) {
        console.error('Error loading appointments:', error);
        setAppointments([]);
      } finally {
        setLoadingAppointments(false);
      }
    };

    loadAppointments();
  }, [currentUser?.uid, userProfile]);

  // Show loading while determining user role
  if (loading || !userProfile) {
    return (
      <Layout>
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '50vh'
        }}>
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  // Only render student dashboard if user is a student
  if (userProfile.role !== 'student') {
    return null; // This will be handled by the useEffect redirect
  }

  // Format appointments for display
  const upcomingAppointments = appointments.slice(0, 4).map(apt => ({
    id: apt.id || '',
    doctor: apt.doctorName,
    specialty: 'Healthcare Provider', // We can enhance this later with doctor specialties
    date: new Date(apt.date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    time: apt.time,
    appointmentData: apt // Keep original data for actions
  }));

  // Mock data for recent health tips
  const recentHealthTips = [
    { id: 1, title: 'Managing Exam Stress', category: 'Mental Health', date: 'May 5, 2023' },
    { id: 2, title: 'Nutrition Tips for Students', category: 'Nutrition', date: 'May 3, 2023' },
    { id: 3, title: 'Importance of Sleep', category: 'Wellness', date: 'April 28, 2023' }
  ];

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Dashboard Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Welcome back, {userProfile?.displayName || currentUser?.displayName || 'User'}
            </Typography>
          </Box>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<HealthAndSafetyIcon />}
            onClick={() => navigate('/symptom-checker')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            Check Symptoms
          </Button>
        </Box>

        <Grid container spacing={3}>
          {/* Quick Actions */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Quick Actions
              </Typography>
              <Divider sx={{ my: 2 }} />
              <List sx={{ py: 0 }}>
                <ListItem
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/profile')}
                >
                  <ListItemIcon>
                    <PersonIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="View Profile" />
                </ListItem>
                <ListItem
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/appointments')}
                >
                  <ListItemIcon>
                    <CalendarMonthIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Book Appointment" />
                </ListItem>
                <ListItem
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/chat')}
                >
                  <ListItemIcon>
                    <ChatIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Messages" />
                </ListItem>
                <ListItem
                  sx={{
                    borderRadius: 2,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                  }}
                  onClick={() => navigate('/health-resources')}
                >
                  <ListItemIcon>
                    <ArticleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Health Resources" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Upcoming Appointments */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Upcoming Appointments
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/appointments')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />

              {loadingAppointments ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : upcomingAppointments.length > 0 ? (
                <Grid container spacing={2}>
                  {upcomingAppointments.map((appointment) => (
                    <Grid item xs={12} sm={6} key={appointment.id}>
                      <Card 
                        sx={{ 
                          borderRadius: 2,
                          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                      >
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                              {appointment.doctor.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {appointment.doctor}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {appointment.specialty}
                              </Typography>
                            </Box>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CalendarMonthIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1 }} />
                            <Typography variant="body2">
                              {appointment.date} at {appointment.time}
                            </Typography>
                          </Box>
                        </CardContent>
                        <CardActions sx={{ p: 2, pt: 0 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            fullWidth
                            sx={{ borderRadius: 2 }}
                            onClick={() => navigate('/appointments')}
                          >
                            View Details
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CalendarMonthIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No upcoming appointments
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    sx={{ mt: 2, borderRadius: 2 }}
                    onClick={() => navigate('/appointments')}
                  >
                    Book Appointment
                  </Button>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Health Tips */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Health Tips
                </Typography>
                <Button 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/health-resources')}
                  sx={{ borderRadius: 2 }}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <List>
                {recentHealthTips.map((tip) => (
                  <ListItem 
                    key={tip.id}
                    sx={{ 
                      px: 2, 
                      py: 1.5, 
                      borderRadius: 2,
                      mb: 1,
                      '&:hover': { 
                        bgcolor: 'rgba(0,114,255,0.08)',
                        cursor: 'pointer'
                      }
                    }}
                    onClick={() => navigate(`/health-resources/${tip.id}`)}
                  >
                    <ListItemIcon>
                      <ArticleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={tip.title}
                      secondary={`${tip.category} • ${tip.date}`}
                      primaryTypographyProps={{ fontWeight: 'medium' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Notifications */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotificationsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Notifications
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              
              <List sx={{ py: 0 }}>
                <ListItem 
                  sx={{ 
                    px: 2, 
                    py: 1.5, 
                    borderRadius: 2,
                    mb: 1,
                    bgcolor: 'rgba(0,114,255,0.08)'
                  }}
                >
                  <ListItemText 
                    primary="Appointment Reminder"
                    secondary="You have an appointment tomorrow at 10:00 AM"
                    primaryTypographyProps={{ fontWeight: 'medium' }}
                  />
                  <Chip 
                    label="New" 
                    size="small" 
                    color="primary" 
                    sx={{ ml: 1 }} 
                  />
                </ListItem>
                <ListItem 
                  sx={{ 
                    px: 2, 
                    py: 1.5, 
                    borderRadius: 2,
                    mb: 1
                  }}
                >
                  <ListItemText 
                    primary="New Health Tip"
                    secondary="Check out our latest article on stress management"
                    primaryTypographyProps={{ fontWeight: 'medium' }}
                  />
                </ListItem>
                <ListItem 
                  sx={{ 
                    px: 2, 
                    py: 1.5, 
                    borderRadius: 2
                  }}
                >
                  <ListItemText 
                    primary="Message from Dr. Johnson"
                    secondary="Your test results are ready for review"
                    primaryTypographyProps={{ fontWeight: 'medium' }}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Key Health Features */}
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mt: 2 }}>
              Key Health Features
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/medications')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <MedicationIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Medications
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Track your medications, set reminders, and manage refills
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/health-metrics')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <MonitorHeartIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Health Metrics
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Monitor your vital signs and track health trends over time
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/medical-records')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <MedicalInformationIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Medical Records
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Access and manage your medical documents securely
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    borderRadius: 3, 
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    height: '100%',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 6px 25px rgba(0,0,0,0.1)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate('/chat')}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <ChatIcon />
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        Chat
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Message your healthcare providers securely
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default Dashboard;





