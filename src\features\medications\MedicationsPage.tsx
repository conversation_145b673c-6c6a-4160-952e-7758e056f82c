import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Grid, Button, 
  TextField, MenuItem, Tabs, Tab, Card, CardContent,
  CircularProgress, Divider, IconButton, Chip, Dialog,
  DialogTitle, DialogContent, DialogActions, FormControl,
  InputLabel, Select, FormHelperText, Switch, FormControlLabel
} from '@mui/material';
import { 
  Medication as MedicationIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Notifications as NotificationsIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';

// Medication type definition
interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  timeOfDay: string[];
  startDate: string;
  endDate: string;
  instructions: string;
  remainingPills: number;
  refillReminder: boolean;
  status: 'active' | 'completed' | 'paused';
}

const MedicationsPage = () => {
  const [medications, setMedications] = useState<Medication[]>([]);
  const [loading, setLoading] = useState(true);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingMedication, setEditingMedication] = useState<Medication | null>(null);
  const [newMedication, setNewMedication] = useState<Partial<Medication>>({
    name: '',
    dosage: '',
    frequency: 'daily',
    timeOfDay: ['morning'],
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    instructions: '',
    remainingPills: 30,
    refillReminder: true,
    status: 'active'
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [activeTab, setActiveTab] = useState(0);
  const { currentUser } = useFirebase();

  useEffect(() => {
    // Mock data
    setTimeout(() => {
      const mockMedications: Medication[] = [
        {
          id: '1',
          name: 'Lisinopril',
          dosage: '10mg',
          frequency: 'daily',
          timeOfDay: ['morning'],
          startDate: '2023-05-01',
          endDate: '2023-08-01',
          instructions: 'Take with food',
          remainingPills: 23,
          refillReminder: true,
          status: 'active'
        },
        {
          id: '2',
          name: 'Metformin',
          dosage: '500mg',
          frequency: 'twice-daily',
          timeOfDay: ['morning', 'evening'],
          startDate: '2023-04-15',
          endDate: '2023-07-15',
          instructions: 'Take with meals',
          remainingPills: 15,
          refillReminder: true,
          status: 'active'
        },
        {
          id: '3',
          name: 'Amoxicillin',
          dosage: '250mg',
          frequency: 'three-times-daily',
          timeOfDay: ['morning', 'afternoon', 'evening'],
          startDate: '2023-05-10',
          endDate: '2023-05-20',
          instructions: 'Complete full course',
          remainingPills: 0,
          refillReminder: false,
          status: 'completed'
        },
        {
          id: '4',
          name: 'Ibuprofen',
          dosage: '400mg',
          frequency: 'as-needed',
          timeOfDay: [],
          startDate: '2023-05-15',
          endDate: '',
          instructions: 'Take for pain as needed, not more than 3 times per day',
          remainingPills: 18,
          refillReminder: false,
          status: 'active'
        }
      ];
      
      setMedications(mockMedications);
      setLoading(false);
    }, 1000);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleOpenAddDialog = () => {
    setEditingMedication(null);
    setNewMedication({
      name: '',
      dosage: '',
      frequency: 'daily',
      timeOfDay: ['morning'],
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      instructions: '',
      remainingPills: 30,
      refillReminder: true,
      status: 'active'
    });
    setErrors({});
    setOpenAddDialog(true);
  };

  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
  };

  const handleEditMedication = (medication: Medication) => {
    setEditingMedication(medication);
    setNewMedication({ ...medication });
    setErrors({});
    setOpenAddDialog(true);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setNewMedication({ ...newMedication, [name]: value });
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  const handleSelectChange = (event: any) => {
    const { name, value } = event.target;
    setNewMedication({ ...newMedication, [name]: value });
  };

  const handleTimeOfDayChange = (event: any) => {
    const { value } = event.target;
    setNewMedication({ ...newMedication, timeOfDay: value });
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    setNewMedication({ ...newMedication, [name]: checked });
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!newMedication.name?.trim()) {
      newErrors.name = 'Medication name is required';
    }
    
    if (!newMedication.dosage?.trim()) {
      newErrors.dosage = 'Dosage is required';
    }
    
    if (!newMedication.startDate) {
      newErrors.startDate = 'Start date is required';
    }
    
    if (newMedication.frequency === 'custom' && (!newMedication.timeOfDay || newMedication.timeOfDay.length === 0)) {
      newErrors.timeOfDay = 'Please select at least one time of day';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveMedication = () => {
    if (!validateForm()) return;
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (editingMedication) {
        // Update existing medication
        const updatedMedications = medications.map(med => 
          med.id === editingMedication.id ? { ...newMedication, id: med.id } as Medication : med
        );
        setMedications(updatedMedications);
      } else {
        // Add new medication
        const newMed: Medication = {
          ...newMedication as Medication,
          id: Date.now().toString()
        };
        setMedications([...medications, newMed]);
      }
      
      setOpenAddDialog(false);
      setLoading(false);
    }, 800);
  };

  const handleDeleteMedication = (id: string) => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const updatedMedications = medications.filter(med => med.id !== id);
      setMedications(updatedMedications);
      setLoading(false);
    }, 500);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'completed':
        return 'info';
      case 'paused':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return 'Once daily';
      case 'twice-daily':
        return 'Twice daily';
      case 'three-times-daily':
        return 'Three times daily';
      case 'weekly':
        return 'Once weekly';
      case 'as-needed':
        return 'As needed';
      default:
        return frequency;
    }
  };

  const getTimeOfDayText = (times: string[]) => {
    if (!times || times.length === 0) return '';
    return times.map(t => t.charAt(0).toUpperCase() + t.slice(1)).join(', ');
  };

  const renderMedicationCards = (status: 'active' | 'completed' | 'paused') => {
    const filteredMeds = medications.filter(med => med.status === status);
    
    if (filteredMeds.length === 0) {
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No {status} medications found
          </Typography>
        </Box>
      );
    }
    
    return (
      <Grid container spacing={3}>
        {filteredMeds.map(medication => (
          <Grid item xs={12} sm={6} md={4} key={medication.id}>
            <Card 
              variant="outlined" 
              sx={{ 
                borderRadius: 2,
                position: 'relative',
                borderColor: medication.remainingPills <= 5 ? 'warning.main' : undefined
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Typography variant="h6" component="div" gutterBottom>
                    {medication.name}
                  </Typography>
                  <Chip 
                    label={medication.status} 
                    color={getStatusColor(medication.status) as any}
                    size="small"
                  />
                </Box>
                
                <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                  {medication.dosage}
                </Typography>
                
                <Divider sx={{ my: 1.5 }} />
                
                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Frequency:
                    </Typography>
                    <Typography variant="body2">
                      {getFrequencyText(medication.frequency)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Time of Day:
                    </Typography>
                    <Typography variant="body2">
                      {getTimeOfDayText(medication.timeOfDay)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} sx={{ mt: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Start Date:
                    </Typography>
                    <Typography variant="body2">
                      {new Date(medication.startDate).toLocaleDateString()}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} sx={{ mt: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      End Date:
                    </Typography>
                    <Typography variant="body2">
                      {medication.endDate ? new Date(medication.endDate).toLocaleDateString() : 'Ongoing'}
                    </Typography>
                  </Grid>
                </Grid>
                
                <Divider sx={{ my: 1.5 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Remaining Pills:
                    </Typography>
                    <Typography 
                      variant="body1" 
                      fontWeight="bold"
                      color={medication.remainingPills <= 5 ? 'warning.main' : 'text.primary'}
                    >
                      {medication.remainingPills}
                    </Typography>
                  </Box>
                  
                  {medication.refillReminder && (
                    <Chip 
                      icon={<NotificationsIcon />} 
                      label="Refill Reminder" 
                      size="small" 
                      color="primary" 
                      variant="outlined"
                    />
                  )}
                </Box>
                
                {medication.remainingPills <= 5 && medication.status === 'active' && (
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mt: 1.5, 
                    p: 1, 
                    bgcolor: 'warning.light',
                    borderRadius: 1
                  }}>
                    <WarningIcon color="warning" fontSize="small" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      Low supply - refill soon
                    </Typography>
                  </Box>
                )}
                
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <IconButton 
                    size="small" 
                    onClick={() => handleEditMedication(medication)}
                    sx={{ mr: 1 }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton 
                    size="small" 
                    color="error"
                    onClick={() => handleDeleteMedication(medication.id)}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Medications
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleOpenAddDialog}
          >
            Add Medication
          </Button>
        </Box>
        
        <Paper
          elevation={0}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'  // Updated to match dashboard style
          }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2,
                fontWeight: 'medium'
              }
            }}
          >
                <Tab label="Active" />
                <Tab label="Completed" />
                <Tab label="Paused" />
              </Tabs>

              <Box sx={{ p: 3 }}>
                {activeTab === 0 && renderMedicationCards('active')}
                {activeTab === 1 && renderMedicationCards('completed')}
                {activeTab === 2 && renderMedicationCards('paused')}
              </Box>
            </>
          )}
        </Paper>
        
        {/* Add/Edit Medication Dialog */}
        <Dialog 
          open={openAddDialog} 
          onClose={handleCloseAddDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {editingMedication ? 'Edit Medication' : 'Add New Medication'}
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="name"
                  label="Medication Name"
                  fullWidth
                  value={newMedication.name || ''}
                  onChange={handleInputChange}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="dosage"
                  label="Dosage"
                  fullWidth
                  value={newMedication.dosage || ''}
                  onChange={handleInputChange}
                  error={!!errors.dosage}
                  helperText={errors.dosage}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Frequency</InputLabel>
                  <Select
                    name="frequency"
                    value={newMedication.frequency || 'daily'}
                    onChange={handleSelectChange}
                    label="Frequency"
                  >
                    <MenuItem value="daily">Once Daily</MenuItem>
                    <MenuItem value="twice-daily">Twice Daily</MenuItem>
                    <MenuItem value="three-times-daily">Three Times Daily</MenuItem>
                    <MenuItem value="weekly">Once Weekly</MenuItem>
                    <MenuItem value="as-needed">As Needed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.timeOfDay}>
                  <InputLabel>Time of Day</InputLabel>
                  <Select
                    multiple
                    name="timeOfDay"
                    value={newMedication.timeOfDay || []}
                    onChange={handleTimeOfDayChange}
                    label="Time of Day"
                    disabled={newMedication.frequency === 'as-needed'}
                  >
                    <MenuItem value="morning">Morning</MenuItem>
                    <MenuItem value="afternoon">Afternoon</MenuItem>
                    <MenuItem value="evening">Evening</MenuItem>
                    <MenuItem value="bedtime">Bedtime</MenuItem>
                  </Select>
                  {errors.timeOfDay && <FormHelperText>{errors.timeOfDay}</FormHelperText>}
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="startDate"
                  label="Start Date"
                  type="date"
                  fullWidth
                  value={newMedication.startDate || ''}
                  onChange={handleInputChange}
                  InputLabelProps={{ shrink: true }}
                  error={!!errors.startDate}
                  helperText={errors.startDate}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="endDate"
                  label="End Date (if applicable)"
                  type="date"
                  fullWidth
                  value={newMedication.endDate || ''}
                  onChange={handleInputChange}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="remainingPills"
                  label="Remaining Pills"
                  type="number"
                  fullWidth
                  value={newMedication.remainingPills || 0}
                  onChange={handleInputChange}
                  InputProps={{ inputProps: { min: 0 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={newMedication.status || 'active'}
                    onChange={handleSelectChange}
                    label="Status"
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="paused">Paused</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newMedication.refillReminder || false}
                      onChange={handleSwitchChange}
                      name="refillReminder"
                      color="primary"
                    />
                  }
                  label="Enable refill reminders"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="instructions"
                  label="Special Instructions"
                  multiline
                  rows={3}
                  fullWidth
                  value={newMedication.instructions || ''}
                  onChange={handleInputChange}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseAddDialog}>Cancel</Button>
            <Button 
              onClick={handleSaveMedication} 
              variant="contained"
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default MedicationsPage;
