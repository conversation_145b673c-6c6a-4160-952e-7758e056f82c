import React, { useState } from 'react';
import { 
  Container, Typo<PERSON>, Box, Grid, Paper, Button,
  TextField, Avatar, Divider, Card, CardContent,
  Chip, Switch, FormControlLabel, Tab, Tabs,
  List, ListItem, ListItemText, ListItemIcon,
  Alert, Snackbar
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import PersonIcon from '@mui/icons-material/Person';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import VerifiedIcon from '@mui/icons-material/Verified';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SecurityIcon from '@mui/icons-material/Security';
import { useFirebase } from '../../contexts/FirebaseContext';

const DoctorProfile = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const [isEditing, setIsEditing] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Mock doctor profile data
  const [profileData, setProfileData] = useState({
    personalInfo: {
      firstName: 'Dr. Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      dateOfBirth: '1985-03-15',
      gender: 'Female',
      address: '123 Medical Center Dr, University City, UC 12345'
    },
    professionalInfo: {
      specialty: 'Internal Medicine',
      licenseNumber: 'MD123456',
      yearsOfExperience: 8,
      department: 'Student Health Services',
      position: 'Senior Physician',
      education: 'MD from Harvard Medical School',
      certifications: ['Board Certified Internal Medicine', 'CPR Certified', 'ACLS Certified']
    },
    availability: {
      mondayToFriday: '9:00 AM - 5:00 PM',
      saturday: '10:00 AM - 2:00 PM',
      sunday: 'Closed',
      emergencyAvailable: true
    },
    preferences: {
      emailNotifications: true,
      smsNotifications: false,
      appointmentReminders: true,
      chatNotifications: true,
      videoCallEnabled: true,
      voiceCallEnabled: true
    },
    stats: {
      totalPatients: 145,
      appointmentsThisMonth: 67,
      averageRating: 4.8,
      yearsAtUniversity: 3
    }
  });

  const [editedData, setEditedData] = useState(profileData);

  const handleEdit = () => {
    setIsEditing(true);
    setEditedData(profileData);
  };

  const handleSave = () => {
    setProfileData(editedData);
    setIsEditing(false);
    setSnackbarMessage('Profile updated successfully!');
    setSnackbarOpen(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedData(profileData);
  };

  const handleInputChange = (section, field, value) => {
    setEditedData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handlePreferenceChange = (field, value) => {
    setEditedData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [field]: value
      }
    }));
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Doctor Profile
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your professional profile and preferences
            </Typography>
          </Box>
          {!isEditing ? (
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={handleEdit}
              sx={{ borderRadius: 2 }}
            >
              Edit Profile
            </Button>
          ) : (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleCancel}
                sx={{ borderRadius: 2 }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSave}
                sx={{ borderRadius: 2 }}
              >
                Save Changes
              </Button>
            </Box>
          )}
        </Box>

        <Grid container spacing={3}>
          {/* Profile Overview */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 3 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                <Avatar 
                  sx={{ 
                    width: 120, 
                    height: 120, 
                    mb: 2,
                    bgcolor: 'primary.main',
                    fontSize: '2rem'
                  }}
                >
                  {profileData.personalInfo.firstName.charAt(0)}{profileData.personalInfo.lastName.charAt(0)}
                </Avatar>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  {profileData.personalInfo.firstName} {profileData.personalInfo.lastName}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Chip 
                    icon={<VerifiedIcon />}
                    label="Verified Doctor" 
                    color="success" 
                    size="small"
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  {profileData.professionalInfo.specialty}
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  {profileData.professionalInfo.department}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Quick Stats */}
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" color="primary.main">
                      {profileData.stats.totalPatients}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Patients
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" color="success.main">
                      {profileData.stats.averageRating}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Avg Rating
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" color="info.main">
                      {profileData.stats.appointmentsThisMonth}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      This Month
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" color="warning.main">
                      {profileData.stats.yearsAtUniversity}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Years Here
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* Availability */}
            <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Availability
              </Typography>
              <List dense>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText 
                    primary="Monday - Friday"
                    secondary={profileData.availability.mondayToFriday}
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText 
                    primary="Saturday"
                    secondary={profileData.availability.saturday}
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText 
                    primary="Sunday"
                    secondary={profileData.availability.sunday}
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText 
                    primary="Emergency Available"
                    secondary={profileData.availability.emergencyAvailable ? 'Yes' : 'No'}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Profile Details */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                  <Tab label="Personal Information" />
                  <Tab label="Professional Details" />
                  <Tab label="Preferences" />
                </Tabs>
              </Box>

              <Box sx={{ p: 3 }}>
                {tabValue === 0 && (
                  <Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Personal Information
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="First Name"
                          value={isEditing ? editedData.personalInfo.firstName : profileData.personalInfo.firstName}
                          onChange={(e) => handleInputChange('personalInfo', 'firstName', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Last Name"
                          value={isEditing ? editedData.personalInfo.lastName : profileData.personalInfo.lastName}
                          onChange={(e) => handleInputChange('personalInfo', 'lastName', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Email"
                          value={isEditing ? editedData.personalInfo.email : profileData.personalInfo.email}
                          onChange={(e) => handleInputChange('personalInfo', 'email', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Phone"
                          value={isEditing ? editedData.personalInfo.phone : profileData.personalInfo.phone}
                          onChange={(e) => handleInputChange('personalInfo', 'phone', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Date of Birth"
                          type="date"
                          value={isEditing ? editedData.personalInfo.dateOfBirth : profileData.personalInfo.dateOfBirth}
                          onChange={(e) => handleInputChange('personalInfo', 'dateOfBirth', e.target.value)}
                          disabled={!isEditing}
                          InputLabelProps={{ shrink: true }}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Gender"
                          value={isEditing ? editedData.personalInfo.gender : profileData.personalInfo.gender}
                          onChange={(e) => handleInputChange('personalInfo', 'gender', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Address"
                          value={isEditing ? editedData.personalInfo.address : profileData.personalInfo.address}
                          onChange={(e) => handleInputChange('personalInfo', 'address', e.target.value)}
                          disabled={!isEditing}
                          multiline
                          rows={2}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {tabValue === 1 && (
                  <Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Professional Details
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Specialty"
                          value={isEditing ? editedData.professionalInfo.specialty : profileData.professionalInfo.specialty}
                          onChange={(e) => handleInputChange('professionalInfo', 'specialty', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="License Number"
                          value={isEditing ? editedData.professionalInfo.licenseNumber : profileData.professionalInfo.licenseNumber}
                          onChange={(e) => handleInputChange('professionalInfo', 'licenseNumber', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Years of Experience"
                          type="number"
                          value={isEditing ? editedData.professionalInfo.yearsOfExperience : profileData.professionalInfo.yearsOfExperience}
                          onChange={(e) => handleInputChange('professionalInfo', 'yearsOfExperience', parseInt(e.target.value))}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Department"
                          value={isEditing ? editedData.professionalInfo.department : profileData.professionalInfo.department}
                          onChange={(e) => handleInputChange('professionalInfo', 'department', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Position"
                          value={isEditing ? editedData.professionalInfo.position : profileData.professionalInfo.position}
                          onChange={(e) => handleInputChange('professionalInfo', 'position', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Education"
                          value={isEditing ? editedData.professionalInfo.education : profileData.professionalInfo.education}
                          onChange={(e) => handleInputChange('professionalInfo', 'education', e.target.value)}
                          disabled={!isEditing}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          Certifications
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {profileData.professionalInfo.certifications.map((cert, index) => (
                            <Chip key={index} label={cert} variant="outlined" />
                          ))}
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {tabValue === 2 && (
                  <Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Notification Preferences
                    </Typography>
                    <List>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <EmailIcon />
                        </ListItemIcon>
                        <ListItemText primary="Email Notifications" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isEditing ? editedData.preferences.emailNotifications : profileData.preferences.emailNotifications}
                              onChange={(e) => handlePreferenceChange('emailNotifications', e.target.checked)}
                              disabled={!isEditing}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <PhoneIcon />
                        </ListItemIcon>
                        <ListItemText primary="SMS Notifications" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isEditing ? editedData.preferences.smsNotifications : profileData.preferences.smsNotifications}
                              onChange={(e) => handlePreferenceChange('smsNotifications', e.target.checked)}
                              disabled={!isEditing}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <NotificationsIcon />
                        </ListItemIcon>
                        <ListItemText primary="Appointment Reminders" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isEditing ? editedData.preferences.appointmentReminders : profileData.preferences.appointmentReminders}
                              onChange={(e) => handlePreferenceChange('appointmentReminders', e.target.checked)}
                              disabled={!isEditing}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                    </List>

                    <Divider sx={{ my: 3 }} />

                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Communication Preferences
                    </Typography>
                    <List>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText primary="Video Call Enabled" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isEditing ? editedData.preferences.videoCallEnabled : profileData.preferences.videoCallEnabled}
                              onChange={(e) => handlePreferenceChange('videoCallEnabled', e.target.checked)}
                              disabled={!isEditing}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText primary="Voice Call Enabled" />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isEditing ? editedData.preferences.voiceCallEnabled : profileData.preferences.voiceCallEnabled}
                              onChange={(e) => handlePreferenceChange('voiceCallEnabled', e.target.checked)}
                              disabled={!isEditing}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                    </List>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {/* Success Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={() => setSnackbarOpen(false)}
        >
          <Alert onClose={() => setSnackbarOpen(false)} severity="success">
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Container>
    </Layout>
  );
};

export default DoctorProfile;
