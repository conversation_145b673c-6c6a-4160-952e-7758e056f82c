// Supabase Configuration and Client
// Handles file storage, database operations, and real-time subscriptions

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Storage bucket names
export const STORAGE_BUCKETS = {
  MEDICAL_RECORDS: 'medical-records',
  HEALTH_METRICS: 'health-metrics',
  PROFILE_IMAGES: 'profile-images',
  HEALTH_TIPS_MEDIA: 'health-tips-media'
} as const;

/**
 * Initialize storage buckets
 */
export const initializeStorageBuckets = async () => {
  try {
    const buckets = Object.values(STORAGE_BUCKETS);
    
    for (const bucketName of buckets) {
      const { data: existingBucket } = await supabase.storage.getBucket(bucketName);
      
      if (!existingBucket) {
        const { error } = await supabase.storage.createBucket(bucketName, {
          public: false, // Private by default for medical data
          allowedMimeTypes: [
            'image/*',
            'application/pdf',
            'text/*',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ],
          fileSizeLimit: 10485760 // 10MB limit
        });
        
        if (error) {
          console.error(`Error creating bucket ${bucketName}:`, error);
        } else {
          console.log(`✅ Created storage bucket: ${bucketName}`);
        }
      }
    }
  } catch (error) {
    console.error('Error initializing storage buckets:', error);
  }
};

/**
 * Upload file to Supabase storage
 */
export const uploadFile = async (
  bucketName: string,
  filePath: string,
  file: File,
  options?: {
    cacheControl?: string;
    contentType?: string;
    upsert?: boolean;
  }
) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: options?.cacheControl || '3600',
        contentType: options?.contentType || file.type,
        upsert: options?.upsert || false
      });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

/**
 * Download file from Supabase storage
 */
export const downloadFile = async (bucketName: string, filePath: string) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .download(filePath);

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

/**
 * Get public URL for a file
 */
export const getFileUrl = (bucketName: string, filePath: string) => {
  const { data } = supabase.storage
    .from(bucketName)
    .getPublicUrl(filePath);

  return data.publicUrl;
};

/**
 * Delete file from Supabase storage
 */
export const deleteFile = async (bucketName: string, filePath: string) => {
  try {
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
};

/**
 * List files in a storage bucket
 */
export const listFiles = async (
  bucketName: string,
  folderPath?: string,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: { column: string; order: 'asc' | 'desc' };
  }
) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .list(folderPath, {
        limit: options?.limit || 100,
        offset: options?.offset || 0,
        sortBy: options?.sortBy || { column: 'name', order: 'asc' }
      });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error listing files:', error);
    throw error;
  }
};

/**
 * Create signed URL for private file access
 */
export const createSignedUrl = async (
  bucketName: string,
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      throw error;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error creating signed URL:', error);
    throw error;
  }
};

/**
 * Generate unique file path for uploads
 */
export const generateFilePath = (
  userId: string,
  category: string,
  fileName: string
): string => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileExtension = fileName.split('.').pop();
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  return `${userId}/${category}/${timestamp}_${sanitizedFileName}`;
};

/**
 * Validate file before upload
 */
export const validateFile = (
  file: File,
  options?: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
  }
): { isValid: boolean; error?: string } => {
  const maxSize = options?.maxSize || 10485760; // 10MB default
  const allowedTypes = options?.allowedTypes || [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'File type not allowed'
    };
  }

  return { isValid: true };
};

// Initialize buckets on module load
initializeStorageBuckets();

export default supabase;
