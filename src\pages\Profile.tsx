import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  LinearProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useFirebase } from '../contexts/FirebaseContext';
import { useAuth } from '../contexts/AuthContext';
import Layout from '../components/layout/Layout';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import EmailIcon from '@mui/icons-material/Email';
import SchoolIcon from '@mui/icons-material/School';
import EventIcon from '@mui/icons-material/Event';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import RefreshIcon from '@mui/icons-material/Refresh';

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const { userProfile, refreshUserProfile } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Mock student data - in a real app, this would come from an API
  const [studentData, setStudentData] = useState({
    id: 1,
    name: currentUser?.displayName || 'John Smith',
    email: currentUser?.email || '<EMAIL>',
    studentId: 'STU2023001',
    department: 'Computer Science',
    year: 'Junior',
    status: 'active',
    joinDate: '2023-01-15',
    phone: '(*************',
    dateOfBirth: '2001-05-15',
    address: '123 University Ave, College Town, ST 12345',
    emergencyContact: {
      name: 'Mary Smith',
      relationship: 'Mother',
      phone: '(*************'
    },
    medicalInfo: {
      bloodType: 'O+',
      allergies: ['Penicillin', 'Peanuts'],
      medications: ['Vitamin D'],
      conditions: ['Asthma (mild)']
    },
    avatar: 'JS'
  });

  const [formData, setFormData] = useState(studentData);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditToggle = () => {
    if (editMode) {
      // Cancel edit - reset form data
      setFormData(studentData);
    }
    setEditMode(!editMode);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev] as any,
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSave = async () => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setStudentData(formData);
      setEditMode(false);
      setLoading(false);
      setSaveSuccess(true);
    }, 1000);
  };

  const handleCloseSnackbar = () => {
    setSaveSuccess(false);
  };

  const handleRefreshProfile = async () => {
    setRefreshing(true);
    try {
      await refreshUserProfile();
      setSaveSuccess(true);
    } catch (error) {
      console.error('Error refreshing profile:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Generate avatar text from name
  const generateAvatar = (name: string) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };

  // Mock recent appointments and medical records
  const recentAppointments = [
    { id: 1, date: '2024-01-20', doctor: 'Dr. Sarah Johnson', type: 'General Checkup', status: 'Completed' },
    { id: 2, date: '2024-01-15', doctor: 'Dr. Michael Chen', type: 'Dermatology', status: 'Completed' },
    { id: 3, date: '2024-02-01', doctor: 'Dr. Emily Wilson', type: 'Mental Health', status: 'Upcoming' }
  ];

  const recentMedicalRecords = [
    { id: 1, date: '2024-01-20', diagnosis: 'Annual Physical', doctor: 'Dr. Sarah Johnson' },
    { id: 2, date: '2024-01-15', diagnosis: 'Skin Rash Treatment', doctor: 'Dr. Michael Chen' },
    { id: 3, date: '2023-12-10', diagnosis: 'Flu Symptoms', doctor: 'Dr. Sarah Johnson' }
  ];

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold">
              My Profile
            </Typography>
            {userProfile && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Current Role: <strong>{userProfile.role}</strong>
              </Typography>
            )}
          </Box>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefreshProfile}
              disabled={refreshing}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh Role'}
            </Button>
            {editMode ? (
              <>
                <Button
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={handleEditToggle}
                  disabled={loading}
                  sx={{ borderRadius: 2, mr: 2 }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                  disabled={loading}
                  sx={{ borderRadius: 2 }}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <Button
                variant="contained"
                startIcon={<EditIcon />}
                onClick={handleEditToggle}
                sx={{ borderRadius: 2 }}
              >
                Edit Profile
              </Button>
            )}
          </Box>
        </Box>

        {/* Student Profile */}
        <Paper
          sx={{
            p: 4,
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar
                sx={{
                  width: 150,
                  height: 150,
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: 'secondary.main'
                }}
              >
                {generateAvatar(studentData.name)}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {studentData.name}
              </Typography>
              <Chip
                label={studentData.status === 'active' ? 'Active Student' : 'Inactive'}
                color={studentData.status === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Chip
                label={`${studentData.department} - ${studentData.year}`}
                color="secondary"
                sx={{ mb: 1 }}
              />
              <Chip
                label={`ID: ${studentData.studentId}`}
                variant="outlined"
                sx={{ mb: 3 }}
              />
            </Grid>

            {/* Student Information */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Student Information
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {studentData.email}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <SchoolIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Department
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {studentData.department}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EventIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Enrollment Date
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {new Date(studentData.joinDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ContactPhoneIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Phone
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {studentData.phone}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Paper>

        {/* Tabs for additional information */}
        <Paper
          sx={{
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          >
            <Tab label="Personal Details" />
            <Tab label="Medical Information" />
            <Tab label="Recent Activity" />
          </Tabs>

          <Box sx={{ p: 4 }}>
            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Personal Details
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      disabled={true} // Email usually can't be changed
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Date of Birth"
                      name="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Department</InputLabel>
                      <Select
                        name="department"
                        value={formData.department}
                        onChange={(e) => handleChange(e as any)}
                        disabled={!editMode}
                        label="Department"
                      >
                        <MenuItem value="Computer Science">Computer Science</MenuItem>
                        <MenuItem value="Engineering">Engineering</MenuItem>
                        <MenuItem value="Business">Business</MenuItem>
                        <MenuItem value="Medicine">Medicine</MenuItem>
                        <MenuItem value="Arts">Arts</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Year</InputLabel>
                      <Select
                        name="year"
                        value={formData.year}
                        onChange={(e) => handleChange(e as any)}
                        disabled={!editMode}
                        label="Year"
                      >
                        <MenuItem value="Freshman">Freshman</MenuItem>
                        <MenuItem value="Sophomore">Sophomore</MenuItem>
                        <MenuItem value="Junior">Junior</MenuItem>
                        <MenuItem value="Senior">Senior</MenuItem>
                        <MenuItem value="Graduate">Graduate</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Address"
                      name="address"
                      value={formData.address}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                      multiline
                      rows={2}
                    />
                  </Grid>

                  {/* Emergency Contact */}
                  <Grid item xs={12}>
                    <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                      Emergency Contact
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Contact Name"
                      name="emergencyContact.name"
                      value={formData.emergencyContact.name}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Relationship"
                      name="emergencyContact.relationship"
                      value={formData.emergencyContact.relationship}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Contact Phone"
                      name="emergencyContact.phone"
                      value={formData.emergencyContact.phone}
                      onChange={handleChange}
                      disabled={!editMode}
                      margin="normal"
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Medical Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <BloodtypeIcon sx={{ mr: 1, color: 'error.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Blood Type
                        </Typography>
                      </Box>
                      <Typography variant="h6" color="error.main">
                        {studentData.medicalInfo.bloodType}
                      </Typography>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <HealthAndSafetyIcon sx={{ mr: 1, color: 'warning.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Allergies
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {studentData.medicalInfo.allergies.map((allergy, index) => (
                          <Chip
                            key={index}
                            label={allergy}
                            color="warning"
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <MedicalServicesIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Current Medications
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {studentData.medicalInfo.medications.map((medication, index) => (
                          <Chip
                            key={index}
                            label={medication}
                            color="primary"
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <LocalHospitalIcon sx={{ mr: 1, color: 'info.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Medical Conditions
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {studentData.medicalInfo.conditions.map((condition, index) => (
                          <Chip
                            key={index}
                            label={condition}
                            color="info"
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Recent Activity
                </Typography>

                {/* Recent Appointments */}
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom sx={{ mt: 3 }}>
                  Recent Appointments
                </Typography>
                <List>
                  {recentAppointments.map((appointment) => (
                    <ListItem key={appointment.id} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <EventIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${appointment.type} with ${appointment.doctor}`}
                        secondary={`${new Date(appointment.date).toLocaleDateString()} - ${appointment.status}`}
                      />
                      <Chip
                        label={appointment.status}
                        color={appointment.status === 'Completed' ? 'success' : 'primary'}
                        variant="outlined"
                        size="small"
                      />
                    </ListItem>
                  ))}
                </List>

                {/* Recent Medical Records */}
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom sx={{ mt: 3 }}>
                  Recent Medical Records
                </Typography>
                <List>
                  {recentMedicalRecords.map((record) => (
                    <ListItem key={record.id} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <MedicalServicesIcon color="secondary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={record.diagnosis}
                        secondary={`${new Date(record.date).toLocaleDateString()} - ${record.doctor}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        </Paper>
      </Container>

      {/* Success Snackbar */}
      <Snackbar
        open={saveSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          {refreshing ? 'Profile role refreshed successfully!' : 'Profile updated successfully!'}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default Profile;