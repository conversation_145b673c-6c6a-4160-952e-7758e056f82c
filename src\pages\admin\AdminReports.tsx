import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
  CardActions,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import MedicalRecordAnalytics from '../../components/medical/MedicalRecordAnalytics';
import {
  generateUserReport,
  generateAppointmentReport,
  generateMedicalReport,
  generateSystemReport,
  generateDashboardReport,
  exportReportAsCSV
} from '../../services/reportsService';

// Icons
import DescriptionIcon from '@mui/icons-material/Description';
import BarChartIcon from '@mui/icons-material/BarChart';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import DownloadIcon from '@mui/icons-material/Download';
import PrintIcon from '@mui/icons-material/Print';
import ShareIcon from '@mui/icons-material/Share';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';

const AdminReports = () => {
  const navigate = useNavigate();
  const [reportType, setReportType] = useState('user');
  const [dateRange, setDateRange] = useState('month');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [loading, setLoading] = useState(false);
  const [generatedReports, setGeneratedReports] = useState<any[]>([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Live data for reports
  const [liveReports, setLiveReports] = useState({
    userReport: null as any,
    appointmentReport: null as any,
    medicalReport: null as any,
    systemReport: null as any
  });

  // Available reports with real functionality
  const availableReports = [
    {
      id: 1,
      title: 'User Registration Report',
      type: 'user',
      icon: <BarChartIcon />,
      description: 'Detailed analytics about user registration, growth trends, and role distribution'
    },
    {
      id: 2,
      title: 'Appointment Analytics',
      type: 'appointment',
      icon: <PieChartIcon />,
      description: 'Comprehensive appointment statistics, doctor performance, and booking trends'
    },
    {
      id: 3,
      title: 'Medical Records Report',
      type: 'medical',
      icon: <TimelineIcon />,
      description: 'Medical record analytics, common diagnoses, and treatment outcomes'
    },
    {
      id: 4,
      title: 'System Performance Report',
      type: 'system',
      icon: <BarChartIcon />,
      description: 'System usage statistics, performance metrics, and error rates'
    },
    {
      id: 5,
      title: 'Comprehensive Dashboard Report',
      type: 'dashboard',
      icon: <TimelineIcon />,
      description: 'Complete overview combining all system analytics and metrics'
    }
  ];

  // Load initial data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const dashboardData = await generateDashboardReport();
      setLiveReports({
        userReport: dashboardData.userReport,
        appointmentReport: dashboardData.appointmentReport,
        medicalReport: dashboardData.medicalReport,
        systemReport: dashboardData.systemReport
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      showSnackbar('Failed to load report data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  // Filter reports based on selected type
  const filteredReports = reportType === 'all'
    ? availableReports
    : availableReports.filter(report => report.type === reportType);

  // Function to toggle analytics view
  const toggleAnalytics = () => {
    setShowAnalytics(!showAnalytics);
  };

  // Generate specific report
  const generateReport = async (reportType: string) => {
    try {
      setLoading(true);
      let reportData;
      let reportName;

      switch (reportType) {
        case 'user':
          reportData = await generateUserReport();
          reportName = 'User Registration Report';
          break;
        case 'appointment':
          reportData = await generateAppointmentReport();
          reportName = 'Appointment Analytics Report';
          break;
        case 'medical':
          reportData = await generateMedicalReport();
          reportName = 'Medical Records Report';
          break;
        case 'system':
          reportData = await generateSystemReport();
          reportName = 'System Performance Report';
          break;
        case 'dashboard':
          reportData = await generateDashboardReport();
          reportName = 'Comprehensive Dashboard Report';
          break;
        default:
          throw new Error('Unknown report type');
      }

      // Add to generated reports list
      const newReport = {
        id: Date.now(),
        name: reportName,
        type: reportType,
        data: reportData,
        generatedAt: new Date(),
        dateRange: dateRange
      };

      setGeneratedReports(prev => [newReport, ...prev]);
      showSnackbar(`${reportName} generated successfully!`, 'success');
    } catch (error) {
      console.error('Error generating report:', error);
      showSnackbar('Failed to generate report', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Export report as CSV
  const exportReport = (report: any) => {
    try {
      let csvData: any[] = [];
      let filename = '';

      switch (report.type) {
        case 'user':
          csvData = [
            { metric: 'Total Users', value: report.data.totalUsers },
            { metric: 'New Users This Month', value: report.data.newUsersThisMonth },
            { metric: 'Active Users', value: report.data.activeUsers },
            ...report.data.usersByRole.map((role: any) => ({
              metric: `${role.role} Users`,
              value: role.count
            }))
          ];
          filename = 'user_report';
          break;
        case 'appointment':
          csvData = [
            { metric: 'Total Appointments', value: report.data.totalAppointments },
            { metric: 'Appointments This Month', value: report.data.appointmentsThisMonth },
            { metric: 'Average Per Day', value: report.data.averageAppointmentsPerDay },
            ...report.data.appointmentsByStatus.map((status: any) => ({
              metric: `${status.status} Appointments`,
              value: status.count
            }))
          ];
          filename = 'appointment_report';
          break;
        case 'medical':
          csvData = [
            { metric: 'Total Records', value: report.data.totalRecords },
            { metric: 'Records This Month', value: report.data.recordsThisMonth },
            { metric: 'Follow-ups Pending', value: report.data.followUpsPending },
            ...report.data.commonDiagnoses.map((diagnosis: any) => ({
              metric: `Diagnosis: ${diagnosis.name}`,
              value: diagnosis.count
            }))
          ];
          filename = 'medical_report';
          break;
        default:
          csvData = [{ metric: 'Report Data', value: JSON.stringify(report.data) }];
          filename = 'report';
      }

      exportReportAsCSV(csvData, filename);
      showSnackbar('Report exported successfully!', 'success');
    } catch (error) {
      console.error('Error exporting report:', error);
      showSnackbar('Failed to export report', 'error');
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Reports & Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate and view reports about system usage and performance
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Report Filters */}
          <Grid item xs={12} md={4}>
            <Paper 
              sx={{ 
                p: 3, 
                height: '100%', 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Report Filters
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ mb: 3 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="report-type-label">Report Type</InputLabel>
                  <Select
                    labelId="report-type-label"
                    id="report-type"
                    value={reportType}
                    label="Report Type"
                    onChange={(e) => setReportType(e.target.value)}
                  >
                    <MenuItem value="all">All Reports</MenuItem>
                    <MenuItem value="user">User Reports</MenuItem>
                    <MenuItem value="appointment">Appointment Reports</MenuItem>
                    <MenuItem value="doctor">Doctor Reports</MenuItem>
                    <MenuItem value="student">Student Reports</MenuItem>
                    <MenuItem value="system">System Reports</MenuItem>
                    <MenuItem value="health">Health Reports</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="date-range-label">Date Range</InputLabel>
                  <Select
                    labelId="date-range-label"
                    id="date-range"
                    value={dateRange}
                    label="Date Range"
                    onChange={(e) => setDateRange(e.target.value)}
                  >
                    <MenuItem value="today">Today</MenuItem>
                    <MenuItem value="week">This Week</MenuItem>
                    <MenuItem value="month">This Month</MenuItem>
                    <MenuItem value="quarter">This Quarter</MenuItem>
                    <MenuItem value="year">This Year</MenuItem>
                    <MenuItem value="custom">Custom Range</MenuItem>
                  </Select>
                </FormControl>
                
                {dateRange === 'custom' && (
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      label="Start Date"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      fullWidth
                      sx={{ mb: 2 }}
                    />
                    <TextField
                      label="End Date"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      fullWidth
                    />
                  </Box>
                )}
              </Box>
              
              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={() => generateReport(reportType)}
                disabled={loading}
                sx={{
                  borderRadius: 2,
                  py: 1.5
                }}
              >
                {loading ? <CircularProgress size={20} sx={{ mr: 1 }} /> : null}
                {loading ? 'Generating...' : 'Generate Report'}
              </Button>
            </Paper>
          </Grid>

          {/* Available Reports */}
          <Grid item xs={12} md={8}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Available Reports
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <Grid container spacing={2}>
                {filteredReports.map((report) => (
                  <Grid item xs={12} sm={6} key={report.id}>
                    <Card 
                      sx={{ 
                        borderRadius: 3,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                      }}
                    >
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Box 
                            sx={{ 
                              p: 1.5, 
                              borderRadius: 2, 
                              bgcolor: 'primary.light', 
                              color: 'primary.main',
                              mr: 2
                            }}
                          >
                            {report.icon}
                          </Box>
                          <Typography variant="h6" fontWeight="medium">
                            {report.title}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {report.description}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2, pt: 0 }}>
                        <Button
                          size="small"
                          startIcon={<DescriptionIcon />}
                          onClick={() => generateReport(report.type)}
                          disabled={loading}
                          sx={{ mr: 1 }}
                        >
                          Generate
                        </Button>
                        <Button
                          size="small"
                          startIcon={<DownloadIcon />}
                          onClick={() => {
                            const existingReport = generatedReports.find(r => r.type === report.type);
                            if (existingReport) {
                              exportReport(existingReport);
                            } else {
                              showSnackbar('Please generate the report first', 'error');
                            }
                          }}
                        >
                          Export
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
              
              {filteredReports.length === 0 && (
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    No reports available for the selected type
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Medical Records Analytics */}
          {reportType === 'medical' && liveReports.medicalReport && (
            <Box sx={{ mt: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2" fontWeight="bold">
                  Medical Records Analytics
                </Typography>
                <Button
                  variant="outlined"
                  onClick={toggleAnalytics}
                  sx={{ borderRadius: 2 }}
                >
                  {showAnalytics ? 'Hide Analytics' : 'Show Analytics'}
                </Button>
              </Box>

              {showAnalytics && (
                <MedicalRecordAnalytics
                  monthlyRecords={[
                    { month: 'This Month', count: liveReports.medicalReport.recordsThisMonth },
                    { month: 'Total', count: liveReports.medicalReport.totalRecords }
                  ]}
                  diagnosisDistribution={liveReports.medicalReport.commonDiagnoses.slice(0, 5).map((d: any) => ({
                    name: d.name,
                    value: d.count
                  }))}
                  doctorWorkload={liveReports.medicalReport.recordsByDoctor.map((d: any) => ({
                    name: d.doctorName,
                    records: d.recordCount
                  }))}
                />
              )}
            </Box>
          )}

          {/* Recent Reports */}
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                mt: 3
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Recently Generated Reports
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              <List>
                {generatedReports.length > 0 ? (
                  generatedReports.slice(0, 5).map((report) => (
                    <ListItem
                      key={report.id}
                      sx={{
                        borderRadius: 2,
                        mb: 1,
                        '&:hover': { bgcolor: 'rgba(0,114,255,0.08)' }
                      }}
                    >
                      <ListItemIcon>
                        <DescriptionIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={report.name}
                        secondary={`Generated on ${report.generatedAt.toLocaleDateString()} at ${report.generatedAt.toLocaleTimeString()}`}
                      />
                      <Button
                        size="small"
                        startIcon={<DownloadIcon />}
                        onClick={() => exportReport(report)}
                        sx={{ mr: 1 }}
                      >
                        Download
                      </Button>
                      <Button
                        size="small"
                        startIcon={<PrintIcon />}
                        onClick={() => window.print()}
                      >
                        Print
                      </Button>
                    </ListItem>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText
                      primary="No reports generated yet"
                      secondary="Generate your first report using the filters above"
                    />
                  </ListItem>
                )}
              </List>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminReports;



