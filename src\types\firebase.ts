
// User types
export interface FirebaseUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
}

// Auth credential types
export interface UserCredential {
  user: FirebaseUser;
}

export interface AuthCredential {
  providerId: string;
  signInMethod: string;
}

// User roles as both type and value
export const UserRoles = {
  STUDENT: 'student',
  DOCTOR: 'doctor',
  ADMIN: 'admin'
} as const;

export type UserRole = typeof UserRoles[keyof typeof UserRoles];

// User profile data
export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  role: UserRole;
  phoneNumber?: string;
  department?: string;
  specialty?: string; // For doctors
  studentId?: string; // For students
  createdAt: Date;
  updatedAt: Date;
}


