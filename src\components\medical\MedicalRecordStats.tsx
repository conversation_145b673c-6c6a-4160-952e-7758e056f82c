import React from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip
} from '@mui/material';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import EventIcon from '@mui/icons-material/Event';
import PeopleIcon from '@mui/icons-material/People';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';

interface MedicalRecordStatsProps {
  totalRecords: number;
  recordsThisMonth: number;
  upcomingFollowUps: number;
  activeStudents: number;
  commonDiagnoses: { name: string; count: number }[];
  activeDoctors: { name: string; recordCount: number }[];
}

const MedicalRecordStats: React.FC<MedicalRecordStatsProps> = ({
  totalRecords,
  recordsThisMonth,
  upcomingFollowUps,
  activeStudents,
  commonDiagnoses,
  activeDoctors
}) => {
  return (
    <Grid container spacing={3}>
      {/* Summary Cards */}
      <Grid item xs={12} md={6} lg={3}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box 
              sx={{ 
                p: 1.5, 
                borderRadius: 2,  
                color: 'primary.main',
                mr: 2
              }}
            >
              <LocalHospitalIcon />
            </Box>
            <Typography variant="h6" fontWeight="medium">
              Total Records
            </Typography>
          </Box>
          <Typography variant="h3" fontWeight="bold">
            {totalRecords}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            <TrendingUpIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
            {recordsThisMonth} new this month
          </Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12} md={6} lg={3}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box 
              sx={{ 
                p: 1.5, 
                borderRadius: 2, 
                color: 'warning.main',
                mr: 2
              }}
            >
              <EventIcon />
            </Box>
            <Typography variant="h6" fontWeight="medium">
              Follow-ups
            </Typography>
          </Box>
          <Typography variant="h3" fontWeight="bold">
            {upcomingFollowUps}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Upcoming follow-up appointments
          </Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12} md={6} lg={3}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box 
              sx={{ 
                p: 1.5, 
                borderRadius: 2, 
                color: 'success.main',
                mr: 2
              }}
            >
              <PeopleIcon />
            </Box>
            <Typography variant="h6" fontWeight="medium">
              Active Students
            </Typography>
          </Box>
          <Typography variant="h3" fontWeight="bold">
            {activeStudents}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Students with medical records
          </Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12} md={6} lg={3}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box 
              sx={{ 
                p: 1.5, 
                borderRadius: 2, 

                color: 'info.main',
                mr: 2
              }}
            >
              <MedicalInformationIcon />
            </Box>
            <Typography variant="h6" fontWeight="medium">
              This Month
            </Typography>
          </Box>
          <Typography variant="h3" fontWeight="bold">
            {recordsThisMonth}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            New records in the last 30 days
          </Typography>
        </Paper>
      </Grid>
      
      {/* Common Diagnoses */}
      <Grid item xs={12} md={6}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Typography variant="h6" fontWeight="medium" gutterBottom>
            Common Diagnoses
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <List disablePadding>
            {commonDiagnoses.map((diagnosis, index) => (
              <ListItem 
                key={index} 
                disablePadding 
                sx={{ 
                  mb: 1.5,
                  display: 'flex',
                  justifyContent: 'space-between'
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <MedicalInformationIcon color="primary" />
                </ListItemIcon>
                <ListItemText 
                  primary={diagnosis.name}
                  primaryTypographyProps={{ fontWeight: 'medium' }}
                />
                <Chip 
                  label={diagnosis.count}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Grid>
      
      {/* Active Doctors */}
      <Grid item xs={12} md={6}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Typography variant="h6" fontWeight="medium" gutterBottom>
            Most Active Doctors
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <List disablePadding>
            {activeDoctors.map((doctor, index) => (
              <ListItem 
                key={index} 
                disablePadding 
                sx={{ 
                  mb: 1.5,
                  display: 'flex',
                  justifyContent: 'space-between'
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <LocalHospitalIcon color="primary" />
                </ListItemIcon>
                <ListItemText 
                  primary={doctor.name}
                  primaryTypographyProps={{ fontWeight: 'medium' }}
                />
                <Chip 
                  label={`${doctor.recordCount} records`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default MedicalRecordStats;