import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import Layout from '../../components/layout/Layout';
import {
  getAllSymptoms,
  getAllConditions,
  addSymptom,
  addCondition,
  updateSymptom,
  updateCondition,
  deleteSymptom,
  deleteCondition,
  importSymptomsFromMock,
  importConditionsFromMock,
  getNextSymptomId,
  getNextConditionId,
  checkSymptomDuplicate,
  checkConditionDuplicate,
  type Symptom,
  type Condition
} from '../../services/symptomConditionService';
import { symptoms as mockSymptoms, conditions as mockConditions } from '../../services/mockSymptomCheckerService';

const ConditionManagement = () => {
  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [symptoms, setSymptoms] = useState<Symptom[]>([]);
  const [conditions, setConditions] = useState<Condition[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Dialog states
  const [symptomDialogOpen, setSymptomDialogOpen] = useState(false);
  const [conditionDialogOpen, setConditionDialogOpen] = useState(false);
  const [editingSymptom, setEditingSymptom] = useState<Symptom | null>(null);
  const [editingCondition, setEditingCondition] = useState<Condition | null>(null);
  
  // Form states
  const [symptomForm, setSymptomForm] = useState({
    id: '',
    name: '',
    category: 'General',
    description: ''
  });
  
  const [conditionForm, setConditionForm] = useState({
    id: '',
    name: '',
    symptoms: [] as string[],
    triage_level: 'self_care' as 'self_care' | 'consultation' | 'emergency',
    description: '',
    treatment: '',
    risk_factors: [] as string[],
    category: 'General'
  });
  
  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [symptomsData, conditionsData] = await Promise.all([
        getAllSymptoms(),
        getAllConditions()
      ]);
      setSymptoms(symptomsData);
      setConditions(conditionsData);
    } catch (error) {
      console.error('Error loading data:', error);
      showSnackbar('Failed to load data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  // Import mock data
  const handleImportMockData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        importSymptomsFromMock(mockSymptoms),
        importConditionsFromMock(mockConditions)
      ]);
      await loadData();
      showSnackbar('Mock data imported successfully!', 'success');
    } catch (error) {
      console.error('Error importing mock data:', error);
      showSnackbar('Failed to import mock data', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Symptom handlers
  const handleAddSymptom = async () => {
    try {
      const nextId = await getNextSymptomId();
      setEditingSymptom(null);
      setSymptomForm({
        id: nextId,
        name: '',
        category: 'General',
        description: ''
      });
      setSymptomDialogOpen(true);
    } catch (error) {
      console.error('Error generating symptom ID:', error);
      showSnackbar('Failed to generate symptom ID', 'error');
    }
  };

  const handleEditSymptom = (symptom: Symptom) => {
    setEditingSymptom(symptom);
    setSymptomForm({
      id: symptom.id,
      name: symptom.name,
      category: symptom.category || 'General',
      description: symptom.description || ''
    });
    setSymptomDialogOpen(true);
  };

  const handleSaveSymptom = async () => {
    try {
      // Check for duplicate name (excluding current symptom if editing)
      const isDuplicate = await checkSymptomDuplicate(
        symptomForm.name,
        editingSymptom?.id
      );

      if (isDuplicate) {
        showSnackbar(`Symptom "${symptomForm.name}" already exists`, 'error');
        return;
      }

      if (editingSymptom) {
        await updateSymptom(editingSymptom.id, {
          name: symptomForm.name,
          category: symptomForm.category,
          description: symptomForm.description
        });
        showSnackbar('Symptom updated successfully!', 'success');
      } else {
        await addSymptom({
          id: symptomForm.id,
          name: symptomForm.name,
          category: symptomForm.category,
          description: symptomForm.description
        });
        showSnackbar('Symptom added successfully!', 'success');
      }
      setSymptomDialogOpen(false);
      await loadData();
    } catch (error) {
      console.error('Error saving symptom:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save symptom';
      showSnackbar(errorMessage, 'error');
    }
  };

  const handleDeleteSymptom = async (symptomId: string) => {
    if (window.confirm('Are you sure you want to delete this symptom?')) {
      try {
        await deleteSymptom(symptomId);
        showSnackbar('Symptom deleted successfully!', 'success');
        await loadData();
      } catch (error) {
        console.error('Error deleting symptom:', error);
        showSnackbar('Failed to delete symptom', 'error');
      }
    }
  };

  // Condition handlers
  const handleAddCondition = async () => {
    try {
      const nextId = await getNextConditionId();
      setEditingCondition(null);
      setConditionForm({
        id: nextId,
        name: '',
        symptoms: [],
        triage_level: 'self_care',
        description: '',
        treatment: '',
        risk_factors: [],
        category: 'General'
      });
      setConditionDialogOpen(true);
    } catch (error) {
      console.error('Error generating condition ID:', error);
      showSnackbar('Failed to generate condition ID', 'error');
    }
  };

  const handleEditCondition = (condition: Condition) => {
    setEditingCondition(condition);
    setConditionForm({
      id: condition.id,
      name: condition.name,
      symptoms: condition.symptoms,
      triage_level: condition.triage_level,
      description: condition.description,
      treatment: condition.treatment,
      risk_factors: condition.risk_factors,
      category: condition.category || 'General'
    });
    setConditionDialogOpen(true);
  };

  const handleSaveCondition = async () => {
    try {
      // Check for duplicate name (excluding current condition if editing)
      const isDuplicate = await checkConditionDuplicate(
        conditionForm.name,
        editingCondition?.id
      );

      if (isDuplicate) {
        showSnackbar(`Condition "${conditionForm.name}" already exists`, 'error');
        return;
      }

      if (editingCondition) {
        await updateCondition(editingCondition.id, {
          name: conditionForm.name,
          symptoms: conditionForm.symptoms,
          triage_level: conditionForm.triage_level,
          description: conditionForm.description,
          treatment: conditionForm.treatment,
          risk_factors: conditionForm.risk_factors,
          category: conditionForm.category
        });
        showSnackbar('Condition updated successfully!', 'success');
      } else {
        await addCondition({
          id: conditionForm.id,
          name: conditionForm.name,
          symptoms: conditionForm.symptoms,
          probability_function: 'function(matchCount, selectedSymptoms, userData) { return matchCount / this.symptoms.length; }',
          triage_level: conditionForm.triage_level,
          description: conditionForm.description,
          treatment: conditionForm.treatment,
          risk_factors: conditionForm.risk_factors,
          category: conditionForm.category
        });
        showSnackbar('Condition added successfully!', 'success');
      }
      setConditionDialogOpen(false);
      await loadData();
    } catch (error) {
      console.error('Error saving condition:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save condition';
      showSnackbar(errorMessage, 'error');
    }
  };

  const handleDeleteCondition = async (conditionId: string) => {
    if (window.confirm('Are you sure you want to delete this condition?')) {
      try {
        await deleteCondition(conditionId);
        showSnackbar('Condition deleted successfully!', 'success');
        await loadData();
      } catch (error) {
        console.error('Error deleting condition:', error);
        showSnackbar('Failed to delete condition', 'error');
      }
    }
  };

  // Filter data based on search
  const filteredSymptoms = symptoms.filter(symptom =>
    symptom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (symptom.category && symptom.category.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredConditions = conditions.filter(condition =>
    condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (condition.category && condition.category.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress size={60} />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Medical Conditions & Symptoms Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage the symptom checker database with symptoms and medical conditions
          </Typography>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={handleImportMockData}
            disabled={loading}
          >
            Import Mock Data ({mockSymptoms.length} symptoms, {mockConditions.length} conditions)
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => {
              const data = { symptoms, conditions };
              const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'symptom-condition-data.json';
              a.click();
            }}
          >
            Export Data
          </Button>
        </Box>

        {/* Search */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Search symptoms or conditions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
            }}
            sx={{ maxWidth: 400 }}
          />
        </Box>

        {/* Tabs */}
        <Paper sx={{ borderRadius: 3, overflow: 'hidden' }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label={`Symptoms (${symptoms.length})`} />
            <Tab label={`Conditions (${conditions.length})`} />
          </Tabs>

          {/* Symptoms Tab */}
          {activeTab === 0 && (
            <Box sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Symptoms Database</Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddSymptom}
                >
                  Add Symptom
                </Button>
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredSymptoms.map((symptom) => (
                      <TableRow key={symptom.id} hover>
                        <TableCell>
                          <Chip label={symptom.id} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell fontWeight="medium">{symptom.name}</TableCell>
                        <TableCell>
                          <Chip label={symptom.category || 'General'} size="small" />
                        </TableCell>
                        <TableCell>
                          {symptom.description ? 
                            (symptom.description.length > 50 ? 
                              `${symptom.description.substring(0, 50)}...` : 
                              symptom.description
                            ) : 
                            '-'
                          }
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleEditSymptom(symptom)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteSymptom(symptom.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    {filteredSymptoms.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                          <Typography variant="body2" color="text.secondary">
                            {searchTerm ? 'No symptoms found matching your search.' : 'No symptoms available. Import mock data to get started.'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Conditions Tab */}
          {activeTab === 1 && (
            <Box sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Medical Conditions Database</Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddCondition}
                >
                  Add Condition
                </Button>
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Symptoms</TableCell>
                      <TableCell>Triage Level</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredConditions.map((condition) => (
                      <TableRow key={condition.id} hover>
                        <TableCell>
                          <Chip label={condition.id} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell fontWeight="medium">{condition.name}</TableCell>
                        <TableCell>
                          <Chip
                            label={`${condition.symptoms.length} symptoms`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={condition.triage_level}
                            size="small"
                            color={
                              condition.triage_level === 'emergency' ? 'error' :
                              condition.triage_level === 'consultation' ? 'warning' : 'success'
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Chip label={condition.category || 'General'} size="small" />
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleEditCondition(condition)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteCondition(condition.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    {filteredConditions.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                          <Typography variant="body2" color="text.secondary">
                            {searchTerm ? 'No conditions found matching your search.' : 'No conditions available. Import mock data to get started.'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </Paper>

        {/* Symptom Dialog */}
        <Dialog open={symptomDialogOpen} onClose={() => setSymptomDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {editingSymptom ? 'Edit Symptom' : 'Add New Symptom'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Symptom ID"
                  value={symptomForm.id}
                  InputProps={{ readOnly: true }}
                  helperText="Auto-generated unique ID"
                  variant="filled"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={symptomForm.category}
                    onChange={(e) => setSymptomForm({ ...symptomForm, category: e.target.value })}
                    label="Category"
                  >
                    <MenuItem value="General">General</MenuItem>
                    <MenuItem value="Respiratory">Respiratory</MenuItem>
                    <MenuItem value="Cardiovascular">Cardiovascular</MenuItem>
                    <MenuItem value="Gastrointestinal">Gastrointestinal</MenuItem>
                    <MenuItem value="Neurological">Neurological</MenuItem>
                    <MenuItem value="Musculoskeletal">Musculoskeletal</MenuItem>
                    <MenuItem value="Dermatological">Dermatological</MenuItem>
                    <MenuItem value="Psychological">Psychological</MenuItem>
                    <MenuItem value="Urinary">Urinary</MenuItem>
                    <MenuItem value="ENT">ENT</MenuItem>
                    <MenuItem value="Eye">Eye</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Symptom Name"
                  value={symptomForm.name}
                  onChange={(e) => setSymptomForm({ ...symptomForm, name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={symptomForm.description}
                  onChange={(e) => setSymptomForm({ ...symptomForm, description: e.target.value })}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSymptomDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={handleSaveSymptom}
              variant="contained"
              disabled={!symptomForm.name.trim()}
            >
              {editingSymptom ? 'Update' : 'Add'} Symptom
            </Button>
          </DialogActions>
        </Dialog>

        {/* Condition Dialog */}
        <Dialog open={conditionDialogOpen} onClose={() => setConditionDialogOpen(false)} maxWidth="lg" fullWidth>
          <DialogTitle>
            {editingCondition ? 'Edit Condition' : 'Add New Condition'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Condition ID"
                  value={conditionForm.id}
                  InputProps={{ readOnly: true }}
                  helperText="Auto-generated unique ID"
                  variant="filled"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Triage Level</InputLabel>
                  <Select
                    value={conditionForm.triage_level}
                    onChange={(e) => setConditionForm({ ...conditionForm, triage_level: e.target.value as any })}
                    label="Triage Level"
                  >
                    <MenuItem value="self_care">Self Care</MenuItem>
                    <MenuItem value="consultation">Consultation</MenuItem>
                    <MenuItem value="emergency">Emergency</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Condition Name"
                  value={conditionForm.name}
                  onChange={(e) => setConditionForm({ ...conditionForm, name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Associated Symptoms (comma-separated IDs)"
                  value={conditionForm.symptoms.join(', ')}
                  onChange={(e) => setConditionForm({
                    ...conditionForm,
                    symptoms: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                  })}
                  placeholder="e.g., s_1, s_2, s_3"
                  helperText="Enter symptom IDs separated by commas"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={conditionForm.description}
                  onChange={(e) => setConditionForm({ ...conditionForm, description: e.target.value })}
                  multiline
                  rows={3}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Treatment"
                  value={conditionForm.treatment}
                  onChange={(e) => setConditionForm({ ...conditionForm, treatment: e.target.value })}
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Risk Factors (comma-separated)"
                  value={conditionForm.risk_factors.join(', ')}
                  onChange={(e) => setConditionForm({
                    ...conditionForm,
                    risk_factors: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                  })}
                  placeholder="e.g., Age, Smoking, Family history"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setConditionDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={handleSaveCondition}
              variant="contained"
              disabled={!conditionForm.name.trim() || !conditionForm.description.trim()}
            >
              {editingCondition ? 'Update' : 'Add'} Condition
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </Layout>
  );
};

export default ConditionManagement;
