import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Chip,
  FormControlLabel,
  Checkbox,
  IconButton,
  Autocomplete,
  Stack,
  Snackbar,
  Alert
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
// Removed MUI date picker imports - using simple date input instead
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import ImageIcon from '@mui/icons-material/Image';
import MedicalRecordImages from '../../components/medical/MedicalRecordImages';

const AdminMedicalRecordForm = () => {
  const navigate = useNavigate();
  const { id, studentId } = useParams();
  const isEditMode = Boolean(id);
  
  const [loading, setLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [students, setStudents] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [images, setImages] = useState([]);
  
  const [formData, setFormData] = useState({
    studentId: studentId ? parseInt(studentId) : '',
    doctorId: '',
    date: new Date(),
    diagnosis: '',
    symptoms: [],
    treatment: '',
    medications: [{ name: '', dosage: '', frequency: '' }],
    notes: '',
    followUpNeeded: false,
    followUpDate: null
  });
  
  const [symptomInput, setSymptomInput] = useState('');
  
  useEffect(() => {
    // Fetch students and doctors data
    const mockStudents = [
      { id: 1, name: 'John Smith' },
      { id: 2, name: 'Emma Davis' },
      { id: 3, name: 'Alex Rodriguez' }
    ];
    
    const mockDoctors = [
      { id: 1, name: 'Dr. Sarah Johnson', specialty: 'Cardiology' },
      { id: 2, name: 'Dr. Michael Chen', specialty: 'Pediatrics' },
      { id: 3, name: 'Dr. Emily Wilson', specialty: 'Psychiatry' }
    ];
    
    setStudents(mockStudents);
    setDoctors(mockDoctors);
    
    if (isEditMode) {
      // In a real app, you would fetch the medical record data from an API
      setLoading(true);
      setTimeout(() => {
        // Mock medical record data
        const mockMedicalRecord = {
          id: parseInt(id),
          studentId: 1,
          doctorId: 1,
          date: new Date('2023-05-15'),
          diagnosis: 'Common cold',
          symptoms: ['Runny nose', 'Sore throat', 'Mild fever'],
          treatment: 'Rest and fluids',
          medications: [
            { name: 'Acetaminophen', dosage: '500mg', frequency: 'Every 6 hours as needed' },
            { name: 'Cough syrup', dosage: '10ml', frequency: 'Every 8 hours as needed' }
          ],
          notes: 'Patient should recover within a week. Follow up if symptoms persist beyond 7 days.',
          followUpNeeded: true,
          followUpDate: new Date('2023-05-22')
        };
        
        setFormData(mockMedicalRecord);
        setLoading(false);
      }, 500);
    }
  }, [id, studentId, isEditMode]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };
  
  const handleDateChange = (name, date) => {
    setFormData(prev => ({
      ...prev,
      [name]: date
    }));
  };
  
  const handleAddSymptom = () => {
    if (symptomInput.trim() !== '') {
      setFormData(prev => ({
        ...prev,
        symptoms: [...prev.symptoms, symptomInput.trim()]
      }));
      setSymptomInput('');
    }
  };
  
  const handleRemoveSymptom = (index) => {
    setFormData(prev => ({
      ...prev,
      symptoms: prev.symptoms.filter((_, i) => i !== index)
    }));
  };
  
  const handleAddMedication = () => {
    setFormData(prev => ({
      ...prev,
      medications: [...prev.medications, { name: '', dosage: '', frequency: '' }]
    }));
  };
  
  const handleRemoveMedication = (index) => {
    setFormData(prev => ({
      ...prev,
      medications: prev.medications.filter((_, i) => i !== index)
    }));
  };
  
  const handleMedicationChange = (index, field, value) => {
    setFormData(prev => {
      const updatedMedications = [...prev.medications];
      updatedMedications[index] = {
        ...updatedMedications[index],
        [field]: value
      };
      return {
        ...prev,
        medications: updatedMedications
      };
    });
  };
  
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // In a real app, you would upload the file to a server
      // For now, we'll create a mock image object with a local URL
      const newImage = {
        id: Date.now(), // Use timestamp as temporary ID
        title: file.name,
        date: new Date().toISOString().split('T')[0],
        imageUrl: URL.createObjectURL(file),
        description: '',
        type: 'Uploaded Image'
      };
      
      setImages(prev => [...prev, newImage]);
    }
  };

  const handleRemoveImage = (imageId) => {
    setImages(prev => prev.filter(image => image.id !== imageId));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // In a real app, you would send the form data to an API
    setLoading(true);
    
    setTimeout(() => {
      console.log('Form submitted:', formData);
      setLoading(false);
      setSaveSuccess(true);
      
      // Redirect after successful save
      setTimeout(() => {
        if (isEditMode) {
          navigate(`/admin/medical-records/${id}`);
        } else {
          navigate(`/admin/students/${formData.studentId}`);
        }
      }, 1500);
    }, 1000);
  };
  
  const handleCloseSnackbar = () => {
    setSaveSuccess(false);
  };
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            {isEditMode ? 'Edit Medical Record' : 'New Medical Record'}
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(-1)}
            sx={{ borderRadius: 2 }}
          >
            Back
          </Button>
        </Box>
        
        {/* Form */}
        <Paper 
          component="form"
          onSubmit={handleSubmit}
          sx={{ 
            p: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel id="student-label">Student</InputLabel>
                <Select
                  labelId="student-label"
                  id="studentId"
                  name="studentId"
                  value={formData.studentId}
                  onChange={handleChange}
                  label="Student"
                  disabled={Boolean(studentId) || loading}
                >
                  {students.map((student) => (
                    <MenuItem key={student.id} value={student.id}>
                      {student.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel id="doctor-label">Doctor</InputLabel>
                <Select
                  labelId="doctor-label"
                  id="doctorId"
                  name="doctorId"
                  value={formData.doctorId}
                  onChange={handleChange}
                  label="Doctor"
                  disabled={loading}
                >
                  {doctors.map((doctor) => (
                    <MenuItem key={doctor.id} value={doctor.id}>
                      {doctor.name} ({doctor.specialty})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Date"
                name="date"
                type="date"
                value={formData.date instanceof Date ? formData.date.toISOString().split('T')[0] : formData.date}
                onChange={(e) => handleDateChange('date', new Date(e.target.value))}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                required
                label="Diagnosis"
                name="diagnosis"
                value={formData.diagnosis}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            {/* Symptoms */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                Symptoms
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <TextField
                  fullWidth
                  label="Add Symptom"
                  value={symptomInput}
                  onChange={(e) => setSymptomInput(e.target.value)}
                  disabled={loading}
                  sx={{ mr: 1 }}
                />
                <Button
                  variant="contained"
                  onClick={handleAddSymptom}
                  disabled={!symptomInput.trim() || loading}
                  sx={{ borderRadius: 2, minWidth: 100 }}
                >
                  Add
                </Button>
              </Box>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.symptoms.map((symptom, index) => (
                  <Chip
                    key={index}
                    label={symptom}
                    onDelete={() => handleRemoveSymptom(index)}
                    disabled={loading}
                  />
                ))}
              </Box>
            </Grid>
            
            {/* Treatment */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                Treatment
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                required
                label="Treatment Plan"
                name="treatment"
                value={formData.treatment}
                onChange={handleChange}
                multiline
                rows={2}
                disabled={loading}
              />
            </Grid>
            
            {/* Medications */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Medications
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleAddMedication}
                  disabled={loading}
                  sx={{ borderRadius: 2 }}
                >
                  Add Medication
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            {formData.medications.map((medication, index) => (
              <Grid item xs={12} key={index}>
                <Paper 
                  elevation={0} 
                  sx={{ 
                    p: 2, 
                    mb: 2, 
                    backgroundColor: 'rgba(0,0,0,0.02)',
                    borderRadius: 2,
                    position: 'relative'
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveMedication(index)}
                    disabled={formData.medications.length === 1 || loading}
                    sx={{ position: 'absolute', top: 8, right: 8 }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        required
                        label="Medication Name"
                        value={medication.name}
                        onChange={(e) => handleMedicationChange(index, 'name', e.target.value)}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        required
                        label="Dosage"
                        value={medication.dosage}
                        onChange={(e) => handleMedicationChange(index, 'dosage', e.target.value)}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        required
                        label="Frequency"
                        value={medication.frequency}
                        onChange={(e) => handleMedicationChange(index, 'frequency', e.target.value)}
                        disabled={loading}
                      />
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            ))}
            
            {/* Notes and Follow-up */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                Notes and Follow-up
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                multiline
                rows={3}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.followUpNeeded}
                    onChange={handleCheckboxChange}
                    name="followUpNeeded"
                    disabled={loading}
                  />
                }
                label="Follow-up needed"
              />
            </Grid>
            
            {formData.followUpNeeded && (
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  required
                  label="Follow-up Date"
                  name="followUpDate"
                  type="date"
                  value={formData.followUpDate instanceof Date ? formData.followUpDate.toISOString().split('T')[0] : formData.followUpDate || ''}
                  onChange={(e) => handleDateChange('followUpDate', new Date(e.target.value))}
                  disabled={loading}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
            )}
            
            {/* Images & Documents */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                Images & Documents
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ mb: 2 }}>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="image-upload"
                  type="file"
                  onChange={handleImageUpload}
                  disabled={loading}
                />
                <label htmlFor="image-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<CloudUploadIcon />}
                    disabled={loading}
                    sx={{ borderRadius: 2 }}
                  >
                    Upload Image
                  </Button>
                </label>
              </Box>
              
              {images.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Attached Images ({images.length})
                  </Typography>
                  <MedicalRecordImages 
                    images={images} 
                    variant="standard"
                    cols={3}
                  />
                </Box>
              )}
            </Grid>
            
            {/* Submit Button */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<SaveIcon />}
                  disabled={loading}
                  sx={{ borderRadius: 2, px: 4 }}
                >
                  {loading ? 'Saving...' : 'Save Medical Record'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Container>
      
      {/* Success Snackbar */}
      <Snackbar
        open={saveSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Medical record {isEditMode ? 'updated' : 'created'} successfully!
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminMedicalRecordForm;
