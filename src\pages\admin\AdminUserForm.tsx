import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Snackbar,
  Alert,
  Avatar,
  FormHelperText,
  IconButton,
  InputAdornment
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { createUser, getUserById, updateUser } from '../../services/userManagementService';
import { useAuth } from '../../contexts/AuthContext';

const AdminUserForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { currentUser } = useAuth();
  const isEditMode = Boolean(id);

  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    role: '',
    status: 'active',
    joinDate: new Date().toISOString().split('T')[0],
    password: '',
    confirmPassword: '',
    specialty: '',
    department: ''
  });

  const [errors, setErrors] = useState({
    password: '',
    confirmPassword: '',
    general: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  // Available roles
  const roles = ['admin', 'doctor', 'student'];

  // Doctor specialties
  const specialties = [
    'General Practice',
    'Internal Medicine',
    'Cardiology',
    'Dermatology',
    'Emergency Medicine',
    'Family Medicine',
    'Neurology',
    'Orthopedics',
    'Pediatrics',
    'Psychiatry',
    'Radiology',
    'Surgery'
  ];

  // Departments
  const departments = [
    'Primary Care',
    'Emergency',
    'Internal Medicine',
    'Surgery',
    'Pediatrics',
    'Mental Health',
    'Radiology',
    'Laboratory',
    'Pharmacy'
  ];
  
  useEffect(() => {
    if (isEditMode && id) {
      // Load user data for editing
      setLoading(true);
      getUserById(id)
        .then((userProfile) => {
          if (userProfile) {
            const formatDate = (date: any): string => {
              if (!date) return new Date().toISOString().split('T')[0];

              try {
                if (date.toDate && typeof date.toDate === 'function') {
                  return date.toDate().toISOString().split('T')[0];
                }
                if (date instanceof Date) {
                  return date.toISOString().split('T')[0];
                }
                return new Date(date).toISOString().split('T')[0];
              } catch {
                return new Date().toISOString().split('T')[0];
              }
            };

            setFormData({
              displayName: userProfile.displayName || '',
              email: userProfile.email || '',
              role: userProfile.role || '',
              status: userProfile.status || 'active',
              joinDate: formatDate(userProfile.createdAt),
              password: '',
              confirmPassword: '',
              specialty: userProfile.specialty || '',
              department: userProfile.department || ''
            });
          }
        })
        .catch((error) => {
          console.error('Error loading user:', error);
          setErrors(prev => ({ ...prev, general: 'Failed to load user data' }));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id, isEditMode]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear errors when user types
    if (name === 'password' || name === 'confirmPassword') {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
      
      // Check password match if both fields have values
      if (name === 'confirmPassword' && formData.password && value) {
        if (formData.password !== value) {
          setErrors(prev => ({
            ...prev,
            confirmPassword: 'Passwords do not match'
          }));
        }
      }
      
      if (name === 'password' && formData.confirmPassword && value) {
        if (formData.confirmPassword !== value) {
          setErrors(prev => ({
            ...prev,
            confirmPassword: 'Passwords do not match'
          }));
        } else {
          setErrors(prev => ({
            ...prev,
            confirmPassword: ''
          }));
        }
      }
    }
  };
  
  const validateForm = () => {
    let valid = true;
    const newErrors = { password: '', confirmPassword: '' };
    
    // Only validate password fields if this is a new user or password is being changed
    if (!isEditMode || (formData.password || formData.confirmPassword)) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
        valid = false;
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
        valid = false;
      }
      
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
        valid = false;
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
        valid = false;
      }
    }
    
    setErrors(newErrors);
    return valid;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors(prev => ({ ...prev, general: '' }));

    try {
      if (isEditMode) {
        // Update existing user
        const updateData = {
          displayName: formData.displayName,
          role: formData.role,
          status: formData.status,
          department: formData.department,
          ...(formData.role === 'doctor' && {
            specialty: formData.specialty
          })
        };

        await updateUser(id!, updateData);
        setSnackbarMessage('User updated successfully!');
      } else {
        // Create new user
        const userData = {
          email: formData.email,
          password: formData.password,
          displayName: formData.displayName,
          role: formData.role as any,
          status: formData.status as any,
          department: formData.department,
          ...(formData.role === 'doctor' && {
            specialty: formData.specialty
          })
        };

        await createUser(userData);
        setSnackbarMessage('User created successfully!');
      }

      setSnackbarSeverity('success');
      setSnackbarOpen(true);

      // Navigate back to users list after a short delay
      setTimeout(() => {
        navigate('/admin/users');
      }, 1500);

    } catch (error) {
      console.error('Error saving user:', error);
      setErrors(prev => ({
        ...prev,
        general: error.message || 'Failed to save user. Please try again.'
      }));
      setSnackbarMessage('Failed to save user. Please try again.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  // Toggle password visibility
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            {isEditMode ? 'Edit User' : 'Add New User'}
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/admin/users')}
            sx={{ borderRadius: 2 }}
          >
            Back to Users
          </Button>
        </Box>
        
        {/* Error Display */}
        {errors.general && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {errors.general}
          </Alert>
        )}

        {/* Form */}
        <Paper
          component="form"
          onSubmit={handleSubmit}
          sx={{
            p: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={3}>
            {/* Avatar Preview */}
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  fontSize: '2rem',
                  bgcolor: formData.role === 'admin' ? 'error.main' :
                           formData.role === 'doctor' ? 'primary.main' : 'secondary.main'
                }}
              >
                {generateAvatar(formData.displayName)}
              </Avatar>
            </Grid>
            
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Full Name"
                name="displayName"
                value={formData.displayName}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Email Address"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Role</InputLabel>
                <Select
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  label="Role"
                  disabled={loading}
                >
                  {roles.map((role) => (
                    <MenuItem key={role} value={role}>
                      {role.charAt(0).toUpperCase() + role.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Status"
                  disabled={loading}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Join Date"
                name="joinDate"
                type="date"
                value={formData.joinDate}
                onChange={handleChange}
                disabled={loading || isEditMode}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            {/* Doctor-specific fields */}
            {formData.role === 'doctor' && (
              <>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Specialty</InputLabel>
                    <Select
                      name="specialty"
                      value={formData.specialty}
                      onChange={handleChange}
                      label="Specialty"
                      disabled={loading}
                    >
                      {specialties.map((specialty) => (
                        <MenuItem key={specialty} value={specialty}>
                          {specialty}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Department</InputLabel>
                    <Select
                      name="department"
                      value={formData.department}
                      onChange={handleChange}
                      label="Department"
                      disabled={loading}
                    >
                      {departments.map((department) => (
                        <MenuItem key={department} value={department}>
                          {department}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            )}
            
            {/* Password Section */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                {isEditMode ? 'Change Password' : 'Set Password'}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {isEditMode && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Leave blank to keep the current password.
                </Typography>
              )}
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={handleChange}
                disabled={loading}
                error={!!errors.password}
                helperText={errors.password}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Confirm Password"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleChange}
                disabled={loading}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle confirm password visibility"
                        onClick={handleToggleConfirmPasswordVisibility}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            
            {/* Submit Button */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                startIcon={<SaveIcon />}
                disabled={loading}
                sx={{ 
                  borderRadius: 2,
                  px: 4,
                  py: 1.5
                }}
              >
                {loading ? 'Saving...' : isEditMode ? 'Update User' : 'Create User'}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </Container>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity} 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminUserForm;